expect_key = ['model.vision_tower.vision_tower.blocks.23.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.self.query.bias', 'model.vision_tower.vision_tower.blocks.33.attn.v_bias', 'model.vision_tower.vision_tower.blocks.23.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.37.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.13.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.7.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.self.value.bias', 'model.vision_tower.vision_tower.blocks.16.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.35.norm2.bias', 'model.vision_tower.vision_tower.blocks.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.3.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.28.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.self.value.bias', 'model.vision_tower.vision_tower.blocks.10.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.output.dense.weight', 'model.vision_tower.vision_tower.blocks.29.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.0.norm1.bias', 'model.vision_tower.vision_tower.blocks.21.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.3.mlp.fc2.bias', 'model.vlm_att_ln.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.30.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.output.dense.weight', 'model.vision_tower.vision_tower.blocks.23.norm1.weight', 'model.vision_tower.vision_tower.blocks.12.norm1.bias', 'model.vision_tower.vision_tower.blocks.29.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.self.query.bias', 'model.vision_tower.vision_tower.blocks.3.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.6.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.1.norm1.bias', 'model.vision_tower.vision_tower.blocks.11.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.self.value.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.26.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.self.key.weight', 'model.vision_tower.vision_tower.blocks.6.norm1.weight', 'model.vision_tower.vision_tower.blocks.15.attn.v_bias', 'model.vision_tower.vision_tower.patch_embed.proj.weight', 'model.vision_tower.vision_tower.blocks.17.attn.v_bias', 'model.vision_tower.vision_tower.blocks.25.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.3.norm1.weight', 'model.vision_tower.vision_tower.blocks.1.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.21.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.22.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.30.mlp.fc1.weight', 'model.vision_tower.vision_tower.pos_embed', 'model.vision_tower.vision_tower.blocks.27.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.17.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.28.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.self.key.bias', 'model.vision_tower.vision_tower.blocks.6.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.output.dense.bias', 'model.vision_tower.vision_tower.patch_embed.proj.bias', 'model.vision_tower.vision_tower.blocks.16.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.5.norm1.bias', 'model.vision_tower.vision_tower.blocks.33.norm2.bias', 'model.vision_tower.vision_tower.blocks.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.0.norm2.weight', 'model.vision_tower.vision_tower.blocks.33.attn.q_bias', 'model.vision_tower.vision_tower.blocks.8.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.28.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.14.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.28.norm2.weight', 'model.vision_tower.vision_tower.blocks.22.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.output_query.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.15.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.19.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.33.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.3.attn.q_bias', 'model.vision_tower.vision_tower.blocks.11.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.22.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.3.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.self.value.bias', 'model.vision_tower.vision_tower.blocks.22.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.27.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.21.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.22.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.intermediate_query.dense.weight', 'model.vision_tower.vision_tower.blocks.38.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.29.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.self.key.bias', 'model.vision_tower.vision_tower.blocks.30.norm1.bias', 'model.vision_tower.vision_tower.blocks.0.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.intermediate.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.7.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.12.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.25.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.36.attn.q_bias', 'model.vision_tower.vision_tower.blocks.7.attn.proj.weight', 'model.vlm_att_encoder.bert.embeddings.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.28.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.19.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.17.norm2.weight', 'model.vision_tower.vision_tower.blocks.10.norm1.weight', 'model.vision_tower.vision_tower.blocks.8.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.output.dense.weight', 'model.vision_tower.vision_tower.blocks.36.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.10.norm2.bias', 'model.vision_tower.vision_tower.blocks.11.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.38.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.36.norm2.bias', 'model.vlm_att_encoder.bert.embeddings.word_embeddings.weight', 'model.vision_tower.vision_tower.blocks.34.norm1.weight', 'model.vision_tower.vision_tower.blocks.36.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.output_query.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.26.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.28.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.4.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.5.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.32.norm1.weight', 'model.vision_tower.vision_tower.blocks.31.attn.v_bias', 'model.vision_tower.vision_tower.blocks.33.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.36.norm1.bias', 'model.vision_tower.vision_tower.blocks.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.18.attn.q_bias', 'model.vision_tower.vision_tower.blocks.15.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.8.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.9.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.20.norm1.bias', 'model.vision_tower.vision_tower.blocks.21.norm2.weight', 'model.vision_tower.vision_tower.blocks.37.attn.q_bias', 'model.vision_tower.vision_tower.blocks.13.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.4.norm2.bias', 'model.vision_tower.vision_tower.blocks.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.27.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.self.value.bias', 'model.vision_tower.vision_tower.blocks.2.attn.q_bias', 'model.vision_tower.vision_tower.blocks.13.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.intermediate.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.34.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.23.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.self.value.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.self.value.bias', 'model.vision_tower.vision_tower.blocks.30.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.14.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.18.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.29.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.13.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.25.norm2.weight', 'model.vision_tower.vision_tower.blocks.29.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.38.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.self.value.bias', 'model.vision_tower.vision_tower.blocks.28.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.output_query.dense.bias', 'model.vision_tower.vision_tower.blocks.34.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.self.key.weight', 'model.vision_tower.vision_tower.blocks.27.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.6.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.18.norm2.weight', 'model.vision_tower.vision_tower.blocks.20.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.33.norm1.weight', 'model.vision_tower.vision_tower.blocks.5.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.7.output.dense.weight', 'model.vision_tower.vision_tower.blocks.26.norm1.bias', 'model.vision_tower.vision_tower.blocks.28.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output_query.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.2.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.25.norm1.bias', 'model.vision_tower.vision_tower.blocks.27.norm2.weight', 'model.vision_tower.vision_tower.blocks.10.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.26.attn.v_bias', 'model.vision_tower.vision_tower.blocks.2.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.self.value.weight', 'model.vlm_att_projector.bias', 'model.vision_tower.vision_tower.blocks.34.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.33.norm1.bias', 'model.vision_tower.vision_tower.blocks.18.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.11.output_query.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.intermediate.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.output_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.37.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.intermediate.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.38.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.output_query.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.18.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.self.query.bias', 'model.vision_tower.vision_tower.blocks.34.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.23.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.output_query.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.24.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.30.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.23.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.1.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.31.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.10.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.16.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.14.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.output.dense.weight', 'model.vision_tower.vision_tower.blocks.35.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.self.query.weight', 'model.vision_tower.vision_tower.blocks.4.attn.v_bias', 'model.vision_tower.vision_tower.blocks.1.attn.q_bias', 'model.vision_tower.vision_tower.blocks.31.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.4.norm1.weight', 'model.vision_tower.vision_tower.blocks.24.norm1.weight', 'model.vision_tower.vision_tower.blocks.6.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.self.key.weight', 'model.vision_tower.vision_tower.blocks.22.norm1.bias', 'model.vision_tower.vision_tower.blocks.0.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.2.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.self.key.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.intermediate.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.1.norm2.weight', 'model.vision_tower.vision_tower.blocks.21.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.27.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.self.query.weight', 'model.vision_tower.vision_tower.blocks.33.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.37.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.self.value.bias', 'model.vision_tower.vision_tower.blocks.16.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.29.attn.q_bias', 'model.vision_tower.vision_tower.blocks.36.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.22.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.self.key.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.37.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.intermediate.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.output_query.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.6.norm2.weight', 'model.vision_tower.vision_tower.blocks.15.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.5.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.7.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.1.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.self.query.bias', 'model.vision_tower.vision_tower.blocks.0.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.24.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.output_query.LayerNorm.bias', 'model.vlm_att_val_projector.weight', 'model.vision_tower.vision_tower.blocks.20.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.15.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.37.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.37.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.output_query.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.output.dense.weight', 'model.vision_tower.vision_tower.blocks.5.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.output.LayerNorm.weight', 'model.vlm_att_ln.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.20.attn.v_bias', 'model.vision_tower.vision_tower.blocks.18.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.17.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.18.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.18.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.13.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.26.attn.q_bias', 'model.vision_tower.vision_tower.blocks.7.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.output.dense.bias', 'model.vision_tower.vision_tower.blocks.32.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.output.dense.bias', 'model.vision_tower.vision_tower.blocks.30.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.14.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.34.attn.v_bias', 'model.vision_tower.vision_tower.blocks.16.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.19.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.7.mlp.fc2.bias', 'model.vlm_att_key_projector.weight', 'model.vision_tower.vision_tower.blocks.16.norm1.weight', 'model.vision_tower.vision_tower.blocks.15.norm2.weight', 'model.vision_tower.vision_tower.blocks.19.norm1.weight', 'model.vision_tower.vision_tower.blocks.22.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.10.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.9.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.4.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.22.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.11.norm1.bias', 'model.vision_tower.vision_tower.blocks.18.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.5.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.15.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.self.value.weight', 'model.vision_tower.vision_tower.blocks.37.attn.v_bias', 'model.vision_tower.vision_tower.blocks.17.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.36.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.7.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.intermediate_query.dense.weight', 'model.vision_tower.vision_tower.blocks.16.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.25.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.self.query.weight', 'model.vision_tower.vision_tower.blocks.3.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.13.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.20.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.33.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.self.key.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.11.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.24.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.25.mlp.fc1.bias', 'model.vision_tower.vision_tower.cls_token', 'model.vision_tower.vision_tower.blocks.32.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output.dense.bias', 'model.vision_tower.vision_tower.blocks.25.attn.q_bias', 'model.vlm_att_projector.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.output_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.14.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.11.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.26.norm2.weight', 'model.vision_tower.vision_tower.blocks.35.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.5.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.1.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.19.norm1.bias', 'model.vision_tower.vision_tower.blocks.36.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.31.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.32.norm1.bias', 'model.vision_tower.vision_tower.blocks.11.attn.q_bias', 'model.vision_tower.vision_tower.blocks.35.norm1.bias', 'model.vision_tower.vision_tower.blocks.34.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.32.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.self.value.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.2.norm2.bias', 'model.vision_tower.vision_tower.blocks.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.5.norm1.weight', 'model.vision_tower.vision_tower.blocks.35.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.7.intermediate_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.26.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.self.value.bias', 'model.vision_tower.vision_tower.blocks.38.norm1.weight', 'model.vision_tower.vision_tower.blocks.22.attn.q_bias', 'model.vision_tower.vision_tower.blocks.17.norm2.bias', 'model.vision_tower.vision_tower.blocks.16.norm1.bias', 'model.vision_tower.vision_tower.blocks.16.norm2.bias', 'model.vision_tower.vision_tower.blocks.10.attn.v_bias', 'model.vision_tower.vision_tower.blocks.4.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.34.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.3.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.5.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.14.norm2.weight', 'model.vision_tower.vision_tower.blocks.15.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.7.attn.v_bias', 'model.vision_tower.vision_tower.blocks.31.norm1.weight', 'model.vision_tower.vision_tower.blocks.27.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.4.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.12.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.9.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.19.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.21.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.10.norm2.weight', 'model.vision_tower.vision_tower.blocks.6.attn.q_bias', 'model.vision_tower.vision_tower.blocks.6.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output.dense.weight', 'model.vision_tower.vision_tower.blocks.32.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.7.attn.q_bias', 'model.vision_tower.vision_tower.blocks.31.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.3.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.intermediate_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.33.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.30.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.17.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.8.norm2.bias', 'model.vision_tower.vision_tower.blocks.9.norm2.bias', 'model.vision_tower.vision_tower.blocks.38.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.21.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.21.attn.v_bias', 'model.vision_tower.vision_tower.blocks.25.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.0.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.27.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.27.norm2.bias', 'model.vision_tower.vision_tower.blocks.32.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.25.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.15.attn.q_bias', 'model.vision_tower.vision_tower.blocks.7.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.7.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.31.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.36.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.2.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.37.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.24.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.29.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.20.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.28.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.38.attn.q_bias', 'model.vision_tower.vision_tower.blocks.34.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.self.value.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.19.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.12.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.self.value.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.output_query.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.29.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.19.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.self.key.bias', 'model.vision_tower.vision_tower.blocks.4.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.1.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.17.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.self.value.weight', 'model.vision_tower.vision_tower.blocks.13.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.2.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.21.norm1.weight', 'model.vision_tower.vision_tower.blocks.15.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.19.norm2.bias', 'model.vision_tower.vision_tower.blocks.14.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.34.attn.q_bias', 'model.vision_tower.vision_tower.blocks.5.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.24.norm2.weight', 'model.vision_tower.vision_tower.blocks.34.norm2.bias', 'model.vision_tower.vision_tower.blocks.28.attn.q_bias', 'model.vision_tower.vision_tower.blocks.30.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.16.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.23.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.0.norm2.bias', 'model.vision_tower.vision_tower.blocks.36.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.23.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.8.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.38.norm2.weight', 'model.vision_tower.vision_tower.blocks.11.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.12.attn.v_bias', 'model.vision_tower.vision_tower.blocks.14.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.35.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.13.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.2.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.26.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.4.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.10.output_query.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.19.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.22.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.self.query.bias', 'model.vision_tower.vision_tower.blocks.17.attn.q_bias', 'model.vision_tower.vision_tower.blocks.8.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.10.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.6.attn.v_bias', 'model.vision_tower.vision_tower.blocks.10.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.23.norm2.bias', 'model.vision_tower.vision_tower.blocks.24.norm2.bias', 'model.vision_tower.vision_tower.blocks.31.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.self.value.bias', 'model.vision_tower.vision_tower.blocks.23.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.4.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.31.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.self.query.weight', 'model.vision_tower.vision_tower.blocks.32.norm2.bias', 'model.vision_tower.vision_tower.blocks.25.norm2.bias', 'model.vision_tower.vision_tower.blocks.18.norm1.weight', 'model.vision_tower.vision_tower.blocks.8.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.intermediate.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.intermediate_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.8.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.self.query.weight', 'model.vision_tower.vision_tower.blocks.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.33.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.self.value.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.13.attn.v_bias', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.self.query.bias', 'model.vision_tower.vision_tower.blocks.6.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.20.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.15.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.output_query.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.32.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.9.norm1.bias', 'model.vision_tower.vision_tower.blocks.20.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.self.query.bias', 'model.vision_tower.vision_tower.blocks.36.norm2.weight', 'model.vision_tower.vision_tower.blocks.10.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.output.LayerNorm.weight', 'model.vision_tower.vision_tower.blocks.38.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.4.norm1.bias', 'model.vision_tower.vision_tower.blocks.8.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.intermediate_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.35.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.28.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.output.dense.bias', 'model.vision_tower.vision_tower.blocks.18.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.output_query.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.25.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.30.attn.v_bias', 'model.vision_tower.vision_tower.blocks.20.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.self.value.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.32.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output_query.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.self.query.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.37.norm1.weight', 'model.vision_tower.vision_tower.blocks.36.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.self.key.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.output_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.self.key.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.21.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.24.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.16.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.2.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.self.key.bias', 'model.vision_tower.vision_tower.blocks.15.norm2.bias', 'model.vision_tower.vision_tower.blocks.1.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.intermediate.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.16.attn.q_bias', 'model.vision_tower.vision_tower.blocks.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.25.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.28.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.output_query.dense.bias', 'model.vision_tower.vision_tower.blocks.18.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.27.norm1.bias', 'model.vision_tower.vision_tower.blocks.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.24.attn.v_bias', 'model.vision_tower.vision_tower.blocks.31.attn.proj.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.crossattention.output.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.output_query.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.7.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.12.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.38.norm2.bias', 'model.vision_tower.vision_tower.blocks.2.norm1.bias', 'model.vision_tower.vision_tower.blocks.5.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.output.dense.bias', 'model.vision_tower.vision_tower.blocks.21.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.11.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.1.norm2.bias', 'model.vision_tower.vision_tower.blocks.9.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.output_query.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.20.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.10.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.32.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.34.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.13.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.12.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.5.intermediate_query.dense.weight', 'model.vision_tower.vision_tower.blocks.10.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.37.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.38.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.12.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.0.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.11.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.34.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.17.norm1.weight', 'model.vision_tower.vision_tower.blocks.21.norm1.bias', 'model.vision_tower.vision_tower.blocks.14.attn.v_bias', 'model.vision_tower.vision_tower.blocks.15.norm1.bias', 'model.vision_tower.vision_tower.blocks.31.norm2.bias', 'model.vision_tower.vision_tower.blocks.33.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.self.key.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.1.intermediate.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.output.dense.weight', 'model.vision_tower.vision_tower.blocks.3.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.output_query.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.35.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.2.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.35.norm2.weight', 'model.vision_tower.vision_tower.blocks.29.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.11.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.31.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.16.attn.v_bias', 'model.vision_tower.vision_tower.blocks.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.24.norm1.bias', 'model.vision_tower.vision_tower.blocks.0.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.intermediate.dense.weight', 'model.vision_tower.vision_tower.blocks.13.attn.q_bias', 'model.vision_tower.vision_tower.blocks.10.norm1.bias', 'model.vision_tower.vision_tower.blocks.35.norm1.weight', 'model.vision_tower.vision_tower.blocks.30.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.29.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.9.attn.q_bias', 'model.vision_tower.vision_tower.blocks.19.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.7.output_query.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.24.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.output_query.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.self.key.bias', 'model.vlm_att_encoder.bert.encoder.layer.4.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.12.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.20.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.26.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.self.key.weight', 'model.vision_tower.vision_tower.blocks.29.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.7.norm1.weight', 'model.vision_tower.vision_tower.blocks.0.attn.v_bias', 'model.vlm_att_encoder.bert.embeddings.position_ids', 'model.vlm_att_key_projector.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.output.dense.weight', 'model.vision_tower.vision_tower.blocks.33.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.31.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.9.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.attention.self.query.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.output.dense.bias', 'model.vision_tower.vision_tower.blocks.14.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.35.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.21.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.1.attention.output.dense.bias', 'model.vision_tower.vision_tower.blocks.25.norm1.weight', 'model.vlm_att_encoder.bert.encoder.layer.0.output.dense.bias', 'model.vision_tower.vision_tower.blocks.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.14.norm1.bias', 'model.vision_tower.vision_tower.blocks.27.attn.q_bias', 'model.vision_tower.vision_tower.blocks.32.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.output.LayerNorm.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.5.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.24.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.26.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.attention.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.37.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.output_query.dense.weight', 'model.vision_tower.vision_tower.blocks.17.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.30.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.5.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.17.norm1.bias', 'model.vision_tower.vision_tower.blocks.32.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.2.crossattention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.output.LayerNorm.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.self.query.bias', 'model.vision_tower.vision_tower.blocks.13.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.2.attention.output.dense.weight', 'model.vision_tower.vision_tower.blocks.23.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.27.norm1.weight', 'model.vision_tower.vision_tower.blocks.24.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.4.crossattention.self.value.bias', 'model.vlm_att_encoder.bert.embeddings.position_embeddings.weight', 'model.vision_tower.vision_tower.blocks.8.mlp.fc2.bias', 'model.vlm_att_encoder.bert.encoder.layer.3.intermediate.dense.bias', 'model.vision_tower.vision_tower.blocks.36.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.10.intermediate_query.dense.weight', 'model.vision_tower.vision_tower.blocks.23.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.29.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.6.crossattention.self.key.bias', 'model.vision_tower.vision_tower.blocks.12.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.13.norm1.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.output.dense.bias', 'model.vision_tower.vision_tower.blocks.26.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.12.attn.proj.bias', 'model.vision_tower.vision_tower.blocks.37.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.self.query.bias', 'model.vlm_att_encoder.bert.encoder.layer.0.intermediate_query.dense.weight', 'model.vision_tower.vision_tower.blocks.26.norm2.bias', 'model.vision_tower.vision_tower.blocks.30.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.9.attn.v_bias', 'model.vision_tower.vision_tower.blocks.35.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.6.norm1.bias', 'model.vision_tower.vision_tower.blocks.6.norm2.bias', 'model.vlm_att_encoder.bert.encoder.layer.1.output.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.14.attn.q_bias', 'model.vision_tower.vision_tower.blocks.22.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.14.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.26.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.38.norm1.bias', 'model.vision_tower.vision_tower.blocks.23.attn.q_bias', 'model.vision_tower.vision_tower.blocks.5.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.9.attention.self.value.bias', 'model.vision_tower.vision_tower.blocks.20.mlp.fc1.weight', 'model.vision_tower.vision_tower.blocks.9.attn.proj.weight', 'model.vision_tower.vision_tower.blocks.17.mlp.fc2.bias', 'model.vision_tower.vision_tower.blocks.11.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.3.intermediate_query.dense.bias', 'model.vision_tower.vision_tower.blocks.19.norm2.weight', 'model.vlm_att_encoder.bert.encoder.layer.10.crossattention.output.dense.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.intermediate.dense.bias', 'model.vlm_att_encoder.bert.encoder.layer.8.output_query.dense.bias', 'model.vision_tower.vision_tower.blocks.12.norm1.weight', 'model.vision_tower.vision_tower.blocks.9.attn.qkv.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.output.LayerNorm.weight', 'model.vlm_att_val_projector.bias', 'model.vision_tower.vision_tower.blocks.0.mlp.fc1.bias', 'model.vlm_att_encoder.bert.encoder.layer.7.attention.self.key.bias', 'model.vision_tower.vision_tower.blocks.28.mlp.fc2.weight', 'model.vlm_att_query', 'model.vision_tower.vision_tower.blocks.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.12.norm2.weight', 'model.vision_tower.vision_tower.blocks.27.attn.v_bias', 'model.vision_tower.vision_tower.blocks.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.blocks.38.attn.proj.bias', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.self.value.weight', 'model.vision_tower.vision_tower.blocks.18.norm1.bias', 'model.vision_tower.vision_tower.blocks.1.attn.qkv.weight', 'model.vision_tower.vision_tower.blocks.7.mlp.fc2.weight', 'model.vlm_att_encoder.bert.encoder.layer.8.crossattention.self.value.weight', 'model.vision_tower.vision_tower.blocks.20.attn.q_bias', 'model.vision_tower.vision_tower.blocks.29.norm1.bias', 'model.vision_tower.vision_tower.blocks.6.attn.proj.bias', 'model.vlm_att_encoder.bert.embeddings.LayerNorm.bias', 'model.vision_tower.vision_tower.blocks.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.blocks.30.attn.q_bias', 'model.vision_tower.vision_tower.blocks.0.attn.q_bias', 'model.vision_tower.vision_tower.blocks.19.attn.q_bias', 'model.vlm_att_encoder.bert.encoder.layer.11.attention.self.query.weight', 'model.vision_tower.vision_tower.blocks.35.mlp.fc1.weight', 'model.vlm_att_encoder.bert.encoder.layer.4.output_query.dense.bias', 'model.vision_tower.vision_tower.blocks.22.norm1.weight']

import ipdb; ipdb.set_trace()