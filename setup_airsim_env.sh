#!/bin/bash

# TravelUAV AirSim环境自动配置脚本 - 增强版
# 支持断点续传、网络重试和错误恢复

# 移除 set -e，改为手动错误处理以支持重试机制
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目根目录
PROJECT_ROOT="/home/<USER>/TravelUAV"
ENVS_DIR="$PROJECT_ROOT/envs"

# 下载配置
HUGGINGFACE_REPO="wangxiangyu0814/TravelUAV_env"
MAX_RETRIES=5
INITIAL_WAIT=1
MAX_WORKERS=4

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 新增：网络连接检查函数
check_network() {
    print_message $BLUE "🌐 检查网络连接..."

    # 检查基本网络连接
    if ! ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        print_message $RED "❌ 网络连接失败"
        return 1
    fi

    # 检查 HuggingFace 连接
    # if command -v curl >/dev/null 2>&1; then
    #     if ! curl -s --connect-timeout 10 https://huggingface.co >/dev/null; then
    #         print_message $YELLOW "⚠️  HuggingFace 连接不稳定，将使用镜像"
    #         export HF_ENDPOINT=https://hf-mirror.com
    #     fi
    # else
    #     print_message $YELLOW "⚠️  curl 未安装，默认使用镜像"
    #     export HF_ENDPOINT=https://hf-mirror.com
    # fi
    
    export HF_ENDPOINT=https://hf-mirror.com
    print_message $GREEN "✅ 网络连接正常"
    return 0
}

# 新增：磁盘空间检查函数
check_disk_space() {
    local target_dir=$1
    local required_gb=${2:-50}  # 默认需要50GB

    print_message $BLUE "💾 检查磁盘空间..."

    # 获取可用空间（GB）
    local available_gb=$(df "$target_dir" | awk 'NR==2 {printf "%.0f", $4/1024/1024}')

    if [ "$available_gb" -lt "$required_gb" ]; then
        print_message $RED "❌ 磁盘空间不足：需要 ${required_gb}GB，可用 ${available_gb}GB"
        return 1
    fi

    print_message $GREEN "✅ 磁盘空间充足：可用 ${available_gb}GB"
    return 0
}

# 新增：重试执行函数
retry_command() {
    local max_attempts=$1
    local wait_time=$2
    shift 2
    local command=("$@")

    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        print_message $CYAN "🔄 尝试 $attempt/$max_attempts: ${command[*]}"

        if "${command[@]}"; then
            print_message $GREEN "✅ 命令执行成功"
            return 0
        else
            local exit_code=$?
            print_message $YELLOW "⚠️  尝试 $attempt 失败 (退出码: $exit_code)"

            if [ $attempt -lt $max_attempts ]; then
                print_message $BLUE "⏳ 等待 ${wait_time}s 后重试..."
                sleep $wait_time
                wait_time=$((wait_time * 2))  # 指数退避
            fi
        fi

        attempt=$((attempt + 1))
    done

    print_message $RED "❌ 命令执行失败，已达到最大重试次数"
    return 1
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔧 检查依赖..."

    # 检查 huggingface-cli
    if ! command -v huggingface-cli &> /dev/null; then
        print_message $RED "❌ huggingface-cli 未安装"
        print_message $YELLOW "请运行: pip install huggingface_hub"
        return 1
    fi

    # 检查 huggingface-cli 版本
    local hf_version=$(huggingface-cli version 2>/dev/null | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
    print_message $GREEN "✅ huggingface-cli 版本: $hf_version"

    # 检查airsim
    if ! python -c "import airsim" 2>/dev/null; then
        print_message $YELLOW "⚠️  AirSim Python包未安装，正在安装..."
        if ! pip install airsim; then
            print_message $RED "❌ AirSim 安装失败"
            return 1
        fi
    fi

    # 检查网络连接
    if ! check_network; then
        print_message $RED "❌ 网络连接检查失败"
        return 1
    fi

    print_message $GREEN "✅ 依赖检查完成"
    return 0
}

# 创建目录结构
create_directories() {
    print_message $BLUE "📁 创建目录结构..."

    # 检查磁盘空间
    if ! check_disk_space "$(dirname "$ENVS_DIR")" 50; then
        return 1
    fi

    # 创建目录
    if ! mkdir -p "$ENVS_DIR" "$ENVS_DIR/closeloop_envs" "$ENVS_DIR/carla_town_envs" "$ENVS_DIR/extra_envs"; then
        print_message $RED "❌ 目录创建失败"
        return 1
    fi

    print_message $GREEN "✅ 目录创建完成"
    return 0
}

# 新增：检查下载完整性
check_download_integrity() {
    local download_dir=$1
    print_message $BLUE "� 检查下载完整性..."

    # 检查关键目录是否存在
    local required_dirs=("closeloop_envs" "carla_town_envs" "extra_envs")
    local missing_dirs=()

    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$download_dir/$dir" ]; then
            missing_dirs+=("$dir")
        fi
    done

    if [ ${#missing_dirs[@]} -gt 0 ]; then
        print_message $YELLOW "⚠️  缺少目录: ${missing_dirs[*]}"
        return 1
    fi

    # 检查是否有可执行文件
    local exec_count=$(find "$download_dir" -name "*.sh" -o -name "*.exe" | wc -l)
    if [ "$exec_count" -eq 0 ]; then
        print_message $YELLOW "⚠️  未找到可执行文件"
        return 1
    fi

    print_message $GREEN "✅ 下载完整性检查通过，找到 $exec_count 个可执行文件"
    return 0
}

# 重写：下载环境文件（支持断点续传）
download_environments() {
    print_message $BLUE "📥 下载AirSim环境文件..."

    cd "$ENVS_DIR" || {
        print_message $RED "❌ 无法进入目录: $ENVS_DIR"
        return 1
    }

    local download_dir="TravelUAV_env"
    local temp_marker=".download_in_progress"

    # 检查是否有未完成的下载
    if [ -f "$temp_marker" ]; then
        print_message $YELLOW "⚠️  检测到未完成的下载，是否继续？(Y/n)"
        read -r response
        if [[ "$response" =~ ^[Nn]$ ]]; then
            rm -f "$temp_marker"
            rm -rf "$download_dir"
        else
            print_message $BLUE "🔄 继续之前的下载..."
        fi
    elif [ -d "$download_dir" ]; then
        # 检查现有下载是否完整
        if check_download_integrity "$download_dir"; then
            print_message $GREEN "✅ 环境文件已存在且完整"
            return 0
        else
            print_message $YELLOW "⚠️  现有文件不完整，是否重新下载？(Y/n)"
            read -r response
            if [[ ! "$response" =~ ^[Nn]$ ]]; then
                rm -rf "$download_dir"
            else
                print_message $BLUE "跳过下载，使用现有文件"
                return 0
            fi
        fi
    fi

    # 创建下载进度标记
    touch "$temp_marker"

    print_message $BLUE "🚀 开始使用 huggingface-cli 下载环境文件..."
    print_message $CYAN "📊 仓库: $HUGGINGFACE_REPO"
    print_message $CYAN "📁 目标目录: $download_dir"
    print_message $CYAN "🔧 最大工作线程: $MAX_WORKERS"

    # 构建下载命令
    local download_cmd=(
        huggingface-cli download "$HUGGINGFACE_REPO"
        --repo-type dataset
        --local-dir "./$download_dir"
        --max-workers "$MAX_WORKERS"
    )

    # 添加安静模式（减少输出）
    if [ "${QUIET_MODE:-false}" = "true" ]; then
        download_cmd+=(--quiet)
    fi

    # 执行下载（带重试）
    if retry_command $MAX_RETRIES $INITIAL_WAIT "${download_cmd[@]}"; then
        # 验证下载完整性
        if check_download_integrity "$download_dir"; then
            rm -f "$temp_marker"
            print_message $GREEN "🎉 环境文件下载完成并验证通过！"
            return 0
        else
            print_message $RED "❌ 下载完成但完整性验证失败"
            return 1
        fi
    else
        print_message $RED "❌ 环境文件下载失败"
        print_message $YELLOW "💡 提示："
        print_message $YELLOW "  1. 检查网络连接"
        print_message $YELLOW "  2. 检查磁盘空间"
        print_message $YELLOW "  3. 稍后重新运行脚本继续下载"
        return 1
    fi
}

# 组织环境文件（增强版）
organize_environments() {
    print_message $BLUE "🗂️  组织环境文件..."

    cd "$ENVS_DIR" || {
        print_message $RED "❌ 无法进入目录: $ENVS_DIR"
        return 1
    }

    local source_dir="TravelUAV_env"
    if [ ! -d "$source_dir" ]; then
        print_message $RED "❌ 环境文件目录不存在: $source_dir"
        return 1
    fi

    # 定义目录映射
    local dirs=("closeloop_envs" "carla_town_envs" "extra_envs")
    local total_files=0

    # 移动文件到正确位置
    for dir in "${dirs[@]}"; do
        if [ -d "$source_dir/$dir" ]; then
            print_message $CYAN "📂 处理目录: $dir"

            # 确保目标目录存在
            mkdir -p "$dir"

            # 复制文件并统计
            if cp -r "$source_dir/$dir/"* "$dir/" 2>/dev/null; then
                local file_count=$(find "$dir" -type f | wc -l)
                total_files=$((total_files + file_count))
                print_message $GREEN "  ✅ 复制了 $file_count 个文件"
            else
                print_message $YELLOW "  ⚠️  目录 $dir 为空或复制失败"
            fi
        else
            print_message $YELLOW "  ⚠️  源目录不存在: $source_dir/$dir"
        fi
    done

    # 设置执行权限
    print_message $CYAN "🔧 设置执行权限..."
    local exec_files=$(find . -name "*.sh" -o -name "*.exe")
    local exec_count=0

    while IFS= read -r file; do
        if [ -n "$file" ]; then
            chmod +x "$file"
            exec_count=$((exec_count + 1))
        fi
    done <<< "$exec_files"

    print_message $GREEN "✅ 环境文件组织完成"
    print_message $CYAN "📊 统计信息："
    print_message $CYAN "  - 总文件数: $total_files"
    print_message $CYAN "  - 可执行文件: $exec_count"

    return 0
}

# 更新AirSim服务器配置
update_airsim_config() {
    print_message $BLUE "⚙️  更新AirSim服务器配置..."
    
    local config_file="$PROJECT_ROOT/airsim_plugin/AirVLNSimulatorServerTool.py"
    
    if [ ! -f "$config_file" ]; then
        print_message $RED "❌ AirSim配置文件不存在: $config_file"
        return 1
    fi
    
    # 备份原文件
    cp "$config_file" "$config_file.backup"
    
    # 更新root_path默认值
    sed -i "s|default=\"/nfs/airport/airdrone/\"|default=\"$ENVS_DIR\"|g" "$config_file"
    
    print_message $GREEN "✅ AirSim配置更新完成"
    print_message $BLUE "   原配置已备份为: $config_file.backup"
}

# 创建测试脚本
create_test_script() {
    print_message $BLUE "📝 创建AirSim测试脚本..."
    
    cat > "$PROJECT_ROOT/Test_airsim_connection.py" << 'EOF'
#!/usr/bin/env python3
"""
AirSim连接测试脚本
"""

import airsim
import time
import sys

def test_airsim_connection():
    """测试AirSim连接"""
    print("=== AirSim连接测试 ===")
    
    try:
        # 连接到AirSim
        client = airsim.MultirotorClient()
        client.confirmConnection()
        print("✅ AirSim连接成功")
        
        # 获取无人机状态
        state = client.getMultirotorState()
        pos = state.kinematics_estimated.position
        print(f"✅ 无人机位置: x={pos.x_val:.2f}, y={pos.y_val:.2f}, z={pos.z_val:.2f}")
        
        # 检查API控制
        client.enableApiControl(True)
        print("✅ API控制启用成功")
        
        # 获取图像（测试相机）
        responses = client.simGetImages([
            airsim.ImageRequest("FrontCamera", airsim.ImageType.Scene, False, False)
        ])
        
        if responses and len(responses) > 0:
            print(f"✅ 相机图像获取成功，图像大小: {len(responses[0].image_data_uint8)} bytes")
        else:
            print("⚠️  相机图像获取失败")
        
        # 禁用API控制
        client.enableApiControl(False)
        
        return True
        
    except Exception as e:
        print(f"❌ AirSim连接失败: {e}")
        print("请确保AirSim服务器正在运行")
        return False

def main():
    """主函数"""
    print("TravelUAV AirSim连接测试")
    print("=" * 40)
    
    success = test_airsim_connection()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 AirSim测试通过！")
        print("💡 您可以开始使用AirSim进行无人机仿真")
    else:
        print("❌ AirSim测试失败")
        print("📝 请检查:")
        print("  1. AirSim服务器是否正在运行")
        print("  2. 端口配置是否正确")
        print("  3. 环境文件是否正确安装")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
EOF
    
    chmod +x "$PROJECT_ROOT/Test_airsim_connection.py"
    print_message $GREEN "✅ 测试脚本创建完成: Test_airsim_connection.py"
}

# 验证安装（增强版）
verify_installation() {
    print_message $BLUE "🔍 验证安装..."

    local total_issues=0

    # 检查环境文件
    local env_count=0
    local env_types=("closeloop_envs" "carla_town_envs" "extra_envs")
    local env_names=("闭环环境" "Carla环境" "额外环境")

    for i in "${!env_types[@]}"; do
        local env_type="${env_types[$i]}"
        local env_name="${env_names[$i]}"
        local env_dir="$ENVS_DIR/$env_type"

        if [ -d "$env_dir" ]; then
            local count=$(find "$env_dir" -name "*.sh" -o -name "*.exe" | wc -l)
            env_count=$((env_count + count))
            if [ "$count" -gt 0 ]; then
                print_message $GREEN "✅ $env_name: $count 个文件"
            else
                print_message $YELLOW "⚠️  $env_name: 目录存在但无可执行文件"
                total_issues=$((total_issues + 1))
            fi
        else
            print_message $YELLOW "⚠️  $env_name: 目录不存在"
            total_issues=$((total_issues + 1))
        fi
    done

    print_message $CYAN "📊 总计环境文件: $env_count 个"

    # 检查权限
    local exec_count=$(find "$ENVS_DIR" -name "*.sh" -executable 2>/dev/null | wc -l)
    local total_scripts=$(find "$ENVS_DIR" -name "*.sh" 2>/dev/null | wc -l)

    if [ "$exec_count" -eq "$total_scripts" ] && [ "$total_scripts" -gt 0 ]; then
        print_message $GREEN "✅ 可执行权限: $exec_count/$total_scripts 个脚本"
    else
        print_message $YELLOW "⚠️  可执行权限: $exec_count/$total_scripts 个脚本"
        if [ "$exec_count" -lt "$total_scripts" ]; then
            total_issues=$((total_issues + 1))
        fi
    fi

    # 检查配置文件
    local config_file="$PROJECT_ROOT/airsim_plugin/AirVLNSimulatorServerTool.py"
    if [ -f "$config_file" ]; then
        if grep -q "$ENVS_DIR" "$config_file" 2>/dev/null; then
            print_message $GREEN "✅ AirSim配置已更新"
        else
            print_message $YELLOW "⚠️  AirSim配置可能未正确更新"
            total_issues=$((total_issues + 1))
        fi
    else
        print_message $YELLOW "⚠️  AirSim配置文件不存在"
        total_issues=$((total_issues + 1))
    fi

    # 检查测试脚本
    if [ -f "$PROJECT_ROOT/Test_airsim_connection.py" ]; then
        print_message $GREEN "✅ 测试脚本已创建"
    else
        print_message $YELLOW "⚠️  测试脚本创建失败"
        total_issues=$((total_issues + 1))
    fi

    # 总结
    print_message $CYAN "📋 验证总结:"
    if [ "$total_issues" -eq 0 ]; then
        print_message $GREEN "🎉 所有检查项目都通过了！"
    else
        print_message $YELLOW "⚠️  发现 $total_issues 个潜在问题"
        print_message $YELLOW "💡 这些问题可能不会影响基本功能"
    fi

    return 0
}

# 新增：显示帮助信息
show_help() {
    print_message $BLUE "📖 TravelUAV AirSim环境配置脚本 - 增强版"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --skip-download    跳过环境文件下载"
    echo "  --quiet           安静模式，减少输出"
    echo "  --help, -h        显示此帮助信息"
    echo ""
    echo "特性:"
    echo "  ✅ 断点续传支持"
    echo "  ✅ 网络中断自动重试"
    echo "  ✅ 下载完整性验证"
    echo "  ✅ 磁盘空间检查"
    echo "  ✅ 智能错误恢复"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整安装"
    echo "  $0 --skip-download    # 跳过下载"
    echo "  $0 --quiet            # 安静模式"
    echo ""
}

# 显示使用说明（增强版）
show_usage() {
    print_message $BLUE "📖 使用说明"
    echo "=================================="
    echo "1. 启动AirSim服务器:"
    echo "   cd $PROJECT_ROOT"
    if [ -f "$PROJECT_ROOT/start_traveluav.sh" ]; then
        echo "   ./start_traveluav.sh airsim"
    else
        echo "   cd airsim_plugin"
        echo "   python AirVLNSimulatorServerTool.py --port 30000 --root_path $ENVS_DIR --gpus 0"
    fi
    echo ""
    echo "2. 测试连接:"
    echo "   python Test_airsim_connection.py"
    echo ""
    echo "3. 运行导航演示:"
    echo "   bash scripts/eval.sh"
    echo ""
    echo "4. 重新下载环境文件:"
    echo "   $0  # 重新运行脚本"
    echo ""
    echo "5. 故障排除:"
    echo "   - 网络问题: 脚本会自动重试"
    echo "   - 磁盘空间: 确保至少50GB可用空间"
    echo "   - 权限问题: 确保对目标目录有写权限"
    echo "=================================="
}

# 主函数（增强版）
main() {
    print_message $GREEN "🚁 TravelUAV AirSim环境配置开始 - 增强版"
    print_message $CYAN "✨ 新特性: 断点续传、网络重试、完整性验证"

    # 解析命令行参数
    local skip_download=false
    local quiet_mode=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-download)
                skip_download=true
                shift
                ;;
            --quiet)
                quiet_mode=true
                export QUIET_MODE=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                print_message $YELLOW "⚠️  未知参数: $1"
                shift
                ;;
        esac
    done

    # 检查是否在正确目录
    if [ ! -f "$PROJECT_ROOT/README.md" ]; then
        print_message $RED "❌ 请确保在TravelUAV项目根目录运行此脚本"
        print_message $YELLOW "当前目录: $(pwd)"
        print_message $YELLOW "期望目录: $PROJECT_ROOT"
        exit 1
    fi

    # 激活环境（如果存在）
    cd "$PROJECT_ROOT" || exit 1
    if [ -f "setup_env.sh" ]; then
        print_message $BLUE "🔧 激活项目环境..."
        source setup_env.sh
    fi

    # 执行配置步骤
    print_message $BLUE "📋 开始执行配置步骤..."

    if ! check_dependencies; then
        print_message $RED "❌ 依赖检查失败"
        exit 1
    fi

    if ! create_directories; then
        print_message $RED "❌ 目录创建失败"
        exit 1
    fi

    # 下载环境文件
    if [ "$skip_download" = true ]; then
        print_message $BLUE "⏭️  跳过环境文件下载（使用 --skip-download 参数）"
    else
        # 询问是否下载环境文件
        if [ "$quiet_mode" = false ]; then
            print_message $YELLOW "是否下载AirSim环境文件？这可能需要很长时间和大量存储空间。(Y/n)"
            read -r download_response
        else
            download_response="y"
        fi

        if [[ ! "$download_response" =~ ^[Nn]$ ]]; then
            if download_environments; then
                if ! organize_environments; then
                    print_message $RED "❌ 环境文件组织失败"
                    exit 1
                fi
            else
                print_message $RED "❌ 环境文件下载失败"
                print_message $YELLOW "💡 您可以稍后使用 --skip-download 参数跳过下载步骤"
                exit 1
            fi
        else
            print_message $BLUE "⏭️  跳过环境文件下载。您可以稍后手动下载。"
        fi
    fi

    # 继续其他配置步骤
    if ! update_airsim_config; then
        print_message $YELLOW "⚠️  AirSim配置更新失败，但继续执行"
    fi

    create_test_script
    verify_installation

    print_message $GREEN "🎉 AirSim环境配置完成！"
    show_usage

    return 0
}

# 错误处理和清理
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        print_message $RED "❌ 脚本执行失败 (退出码: $exit_code)"

        # 清理临时文件
        if [ -f "$ENVS_DIR/.download_in_progress" ]; then
            print_message $YELLOW "🧹 清理下载进度标记..."
            rm -f "$ENVS_DIR/.download_in_progress"
        fi

        print_message $YELLOW "💡 故障排除建议:"
        print_message $YELLOW "  1. 检查网络连接"
        print_message $YELLOW "  2. 确保有足够的磁盘空间"
        print_message $YELLOW "  3. 检查目录权限"
        print_message $YELLOW "  4. 重新运行脚本以继续下载"
    fi
}

# 设置错误处理
trap cleanup EXIT

# 运行主函数
if main "$@"; then
    print_message $GREEN "✨ 脚本执行成功完成！"
    exit 0
else
    print_message $RED "❌ 脚本执行失败"
    exit 1
fi
