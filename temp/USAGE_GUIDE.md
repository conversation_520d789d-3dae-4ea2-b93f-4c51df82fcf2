# 🚁 TravelUAV 详细使用指南

## 📋 目录
1. [演示示例（Demo）](#1-演示示例demo)
2. [项目使用指南](#2-项目使用指南)
3. [数据采集方法](#3-数据采集方法)
4. [Linux可视化界面](#4-linux可视化界面)

---

## 1. 📺 演示示例（Demo）

### 1.1 Gradio Web界面演示

我们为您创建了一个基本的Gradio演示界面：

```bash
# 启动环境
source setup_env.sh

# 运行Gradio演示
python demo_gradio.py
```

**访问方式：**
- 本地访问: http://localhost:7860
- 远程访问: http://YOUR_SERVER_IP:7860

**演示功能：**
- ✅ 环境状态检查
- ✅ 模型加载测试
- ✅ 导航指令处理演示
- ✅ 项目信息展示

### 1.2 GroundingDINO目标检测演示

```bash
# 进入GroundingDINO目录
cd src/model_wrapper/utils/GroundingDINO

# 下载预训练权重
mkdir -p weights
cd weights
wget https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth
cd ..

# 运行目标检测演示
CUDA_VISIBLE_DEVICES=0 python demo/inference_on_a_image.py \
    -c groundingdino/config/GroundingDINO_SwinT_OGC.py \
    -p weights/groundingdino_swint_ogc.pth \
    -i your_image.jpg \
    -o output_dir \
    -t "building . car . person"
```

---

## 2. 🎯 项目使用指南

### 2.1 预训练模型下载

**必需的预训练模型：**

```bash
# 创建模型目录
mkdir -p Model/LLaMA-UAV/work_dirs
mkdir -p Model/LLaMA-UAV/model_zoo

# 1. LLaMA-UAV主模型 (约14GB)
# 从HuggingFace下载到: work_dirs/llama-vid-7b-pretrain-224-uav-full-data-lora32
huggingface-cli download wangxiangyu0814/llama-uav-7b \
    --local-dir Model/LLaMA-UAV/work_dirs/llama-vid-7b-pretrain-224-uav-full-data-lora32

# 2. Vicuna-7B基础模型 (约13GB)
huggingface-cli download lmsys/vicuna-7b-v1.5 \
    --local-dir Model/LLaMA-UAV/model_zoo/vicuna-7b-v1.5

# 3. EVA-ViT-G视觉编码器 (约1.7GB)
mkdir -p Model/LLaMA-UAV/model_zoo/LAVIS
wget https://storage.googleapis.com/sfr-vision-language-research/LAVIS/models/BLIP2/eva_vit_g.pth \
    -O Model/LLaMA-UAV/model_zoo/LAVIS/eva_vit_g.pth

# 4. 轨迹完成模型
huggingface-cli download wangxiangyu0814/traveluav-traj-model \
    --local-dir Model/LLaMA-UAV/work_dirs/traj_predictor_bs_128_drop_0.1_lr_5e-4

# 5. CLIP图像处理器
huggingface-cli download openai/clip-vit-large-patch14-336 \
    --local-dir Model/LLaMA-UAV/llamavid/processor/clip-patch14-224
```

### 2.2 主要入口脚本

#### 2.2.1 模型训练

```bash
# 1. 训练UAV导航LLM
cd Model/LLaMA-UAV
bash scripts/llm/train_uav_llm.sh

# 2. 训练轨迹完成模型
bash scripts/traj/train_traj_completion.sh
```

#### 2.2.2 模型评估

```bash
# 闭环评估
bash scripts/eval.sh

# 计算评估指标
bash scripts/metric.sh
```

#### 2.2.3 数据收集（DAgger）

```bash
# NYC环境数据收集
bash scripts/dagger_NYC.sh
```

### 2.3 配置文件说明

**关键配置文件：**
- `src/common/param.py`: 主要参数配置
- `airsim_plugin/AirVLNSimulatorServerTool.py`: AirSim环境配置
- `Model/LLaMA-UAV/scripts/zero2.json`: DeepSpeed配置

**需要修改的路径：**
```python
# 在脚本中更新以下路径
root_dir="/home/<USER>/TravelUAV"
model_dir="/home/<USER>/TravelUAV/Model/LLaMA-UAV"
dataset_path="/path/to/your/dataset"
```

---

## 3. 📊 数据采集方法

### 3.1 AirSim仿真环境配置

#### 3.1.1 下载仿真环境

```bash
# 创建环境目录
mkdir -p envs/{closeloop_envs,carla_town_envs,extra_envs}

# 下载环境文件（需要从项目页面获取）
# https://huggingface.co/datasets/wangxiangyu0814/TravelUAV_env
```

#### 3.1.2 启动仿真服务器

```bash
# 启动AirSim环境服务器
cd airsim_plugin
python AirVLNSimulatorServerTool.py \
    --port 30000 \
    --root_path /path/to/your/envs \
    --gpus 0,1,2,3
```

### 3.2 数据采集流程

#### 3.2.1 专家数据收集
```bash
# 使用专家策略收集初始数据
CUDA_VISIBLE_DEVICES=0 python src/vlnce_src/dagger.py \
    --run_type collect \
    --collect_type expert \
    --dataset_path /path/to/dataset \
    --dagger_save_path data/expert_data
```

#### 3.2.2 DAgger数据收集
```bash
# 使用DAgger方法收集改进数据
CUDA_VISIBLE_DEVICES=0 python src/vlnce_src/dagger.py \
    --run_type collect \
    --collect_type dagger \
    --dagger_it 5 \
    --dagger_p 0.4
```

### 3.3 数据格式

**数据存储结构：**
```
data/
├── uav_dataset/
│   ├── train.json          # 训练数据索引
│   ├── val.json            # 验证数据索引
│   └── test.json           # 测试数据索引
├── images/                 # RGB图像
├── depth/                  # 深度图像
└── trajectories/           # 轨迹数据
```

**数据格式示例：**
```json
{
    "episode_id": "001",
    "instruction": "飞到红色建筑物前面",
    "trajectory": [
        {
            "position": [x, y, z],
            "rotation": [roll, pitch, yaw],
            "image_path": "images/001_001.jpg",
            "depth_path": "depth/001_001.npy"
        }
    ]
}
```

---

## 4. 🖥️ Linux可视化界面

### 4.1 Gradio Web界面

#### 4.1.1 启动基本演示界面

```bash
# 启动我们创建的演示界面
source setup_env.sh
python demo_gradio.py

# 界面将在以下地址可用：
# 本地: http://localhost:7860
# 远程: http://YOUR_SERVER_IP:7860
```

#### 4.1.2 创建高级可视化界面

让我为您创建一个更高级的可视化界面：

```bash
# 创建高级可视化脚本
python create_advanced_demo.py
```

### 4.2 远程访问配置

#### 4.2.1 SSH隧道访问
```bash
# 在本地机器上运行
ssh -L 7860:localhost:7860 username@your_server_ip

# 然后在本地浏览器访问: http://localhost:7860
```

#### 4.2.2 防火墙配置
```bash
# 开放端口（如果需要）
sudo ufw allow 7860
```

### 4.3 实时可视化

#### 4.3.1 AirSim可视化
```bash
# 启动AirSim环境（带图形界面）
cd envs/closeloop_envs
./ModularPark.sh -windowed -ResX=1920 -ResY=1080
```

#### 4.3.2 轨迹可视化
```python
# 使用matplotlib进行轨迹可视化
import matplotlib.pyplot as plt
import numpy as np

def visualize_trajectory(trajectory_data):
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制轨迹
    positions = np.array([point['position'] for point in trajectory_data])
    ax.plot(positions[:, 0], positions[:, 1], positions[:, 2], 'b-', linewidth=2)
    
    # 标记起点和终点
    ax.scatter(*positions[0], color='green', s=100, label='起点')
    ax.scatter(*positions[-1], color='red', s=100, label='终点')
    
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.legend()
    plt.show()
```

---

## 🚀 快速开始流程

### 完整启动流程：

1. **环境准备**
```bash
source setup_env.sh
```

2. **启动演示界面**
```bash
python demo_gradio.py
```

3. **下载必要模型**（可选，用于完整功能）
```bash
# 下载LLaMA-UAV模型
huggingface-cli download wangxiangyu0814/llama-uav-7b --local-dir Model/LLaMA-UAV/work_dirs/llama-vid-7b-pretrain-224-uav-full-data-lora32
```

4. **配置AirSim环境**（可选，用于仿真）
```bash
cd airsim_plugin
python AirVLNSimulatorServerTool.py --port 30000 --root_path /path/to/envs
```

---

## 📞 故障排除

### 常见问题：

1. **显存不足**
   - 减少batch_size
   - 使用gradient_checkpointing
   - 启用DeepSpeed ZeRO

2. **模型下载慢**
   - 使用HuggingFace镜像（已配置）
   - 使用断点续传

3. **AirSim连接失败**
   - 检查端口是否被占用
   - 确认环境文件路径正确

4. **Gradio界面无法访问**
   - 检查防火墙设置
   - 确认端口7860未被占用

---

## 🎉 快速启动命令

### 一键启动演示
```bash
# 启动基本演示界面
./start_traveluav.sh demo

# 启动高级可视化界面
./start_traveluav.sh advanced

# 检查系统状态
./start_traveluav.sh check
```

### 模型下载
```bash
# 下载所有必需模型
./download_models.sh all

# 检查已下载模型
./download_models.sh check
```

### 当前可用功能
✅ **基本Gradio演示界面** - http://localhost:7860
✅ **环境状态检查和监控**
✅ **导航指令处理演示**
✅ **GroundingDINO目标检测**
✅ **3D轨迹可视化**
✅ **模型下载和管理**

### 需要额外配置的功能
⚠️ **完整模型推理** - 需要下载预训练模型 (~30GB)
⚠️ **AirSim仿真环境** - 需要下载环境文件
⚠️ **真实数据训练** - 需要TravelUAV数据集

---

**💡 提示**: 这个指南涵盖了TravelUAV的主要使用方法。对于特定的研究需求，建议参考项目论文和官方文档。
