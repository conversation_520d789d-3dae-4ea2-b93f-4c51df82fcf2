#!/usr/bin/env python3
"""
高级增量下载脚本 - 支持单文件进度显示和并行下载
"""

from huggingface_hub import HfApi, hf_hub_download
import os
import requests
from pathlib import Path
from typing import List, Dict
import time
from tqdm import tqdm
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

class AdvancedDownloader:
    def __init__(self, repo_id: str, local_dir: str, repo_type: str = "dataset", max_workers: int = 3):
        self.repo_id = repo_id
        self.local_dir = Path(local_dir)
        self.repo_type = repo_type
        self.max_workers = max_workers
        self.api = HfApi()
        
        # 设置HuggingFace镜像
        os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
        
        # 确保目标目录存在
        self.local_dir.mkdir(parents=True, exist_ok=True)
        
        # 用于存储下载统计
        self.download_stats = {
            'total_files': 0,
            'completed_files': 0,
            'failed_files': 0,
            'total_size': 0,
            'downloaded_size': 0
        }
    
    def get_remote_files(self) -> Dict[str, Dict]:
        """获取远程仓库的文件列表和元数据"""
        print(f"正在获取远程仓库 {self.repo_id} 的文件列表...")
        
        try:
            repo_info = self.api.repo_info(
                repo_id=self.repo_id,
                repo_type=self.repo_type
            )
            
            remote_files = {}
            for file_info in repo_info.siblings:
                remote_files[file_info.rfilename] = {
                    'size': getattr(file_info, 'size', 0),
                    'lfs': getattr(file_info, 'lfs', None)
                }
            
            print(f"远程仓库共有 {len(remote_files)} 个文件")
            return remote_files
            
        except Exception as e:
            print(f"获取远程文件列表失败: {e}")
            return {}
    
    def get_local_files(self) -> Dict[str, Dict]:
        """获取本地文件列表和元数据（过滤掉缓存文件）"""
        print(f"正在扫描本地目录 {self.local_dir}...")
        
        local_files = {}
        if self.local_dir.exists():
            for file_path in self.local_dir.rglob('*'):
                if file_path.is_file():
                    relative_path = file_path.relative_to(self.local_dir)
                    relative_path_str = str(relative_path)
                    
                    # 过滤掉缓存文件和临时文件
                    if (relative_path_str.startswith('.cache/') or 
                        relative_path_str.endswith('.lock') or 
                        relative_path_str.endswith('.metadata') or
                        '.incomplete' in relative_path_str):
                        continue
                    
                    local_files[relative_path_str] = {
                        'size': file_path.stat().st_size,
                        'path': file_path
                    }
        
        print(f"本地目录共有 {len(local_files)} 个有效文件")
        return local_files
    
    def identify_missing_files(self, remote_files: Dict, local_files: Dict) -> List[str]:
        """识别缺失的文件"""
        missing_files = []
        
        print("\n正在比较本地和远程文件...")
        
        for remote_file, remote_info in remote_files.items():
            if remote_file not in local_files:
                missing_files.append(remote_file)
                print(f"缺失文件: {remote_file}")
            else:
                # 对于LFS文件（remote_size为None），只检查文件是否存在
                remote_size = remote_info['size']
                local_size = local_files[remote_file]['size']
                
                if remote_size is not None and remote_size > 0 and local_size != remote_size:
                    missing_files.append(remote_file)
                    print(f"文件大小不匹配: {remote_file} (本地: {local_size}, 远程: {remote_size})")
                elif local_size == 0:
                    missing_files.append(remote_file)
                    print(f"本地文件为空: {remote_file}")
        
        print(f"\n发现 {len(missing_files)} 个需要下载的文件")
        return missing_files
    
    def download_single_file_with_progress(self, filename: str, progress_queue: queue.Queue, position: int) -> Dict:
        """下载单个文件，支持详细进度显示"""
        result = {
            'filename': filename,
            'success': False,
            'size': 0,
            'elapsed_time': 0,
            'error': None
        }

        try:
            start_time = time.time()

            # 确保目标目录存在
            target_path = self.local_dir / filename
            target_path.parent.mkdir(parents=True, exist_ok=True)

            # 通知开始下载
            progress_queue.put({
                'type': 'started',
                'filename': filename,
                'position': position
            })

            # 使用huggingface_hub下载，但添加进度监控
            downloaded_path = hf_hub_download(
                repo_id=self.repo_id,
                filename=filename,
                repo_type=self.repo_type,
                local_dir=self.local_dir
            )

            # 计算统计信息
            result['elapsed_time'] = time.time() - start_time
            result['size'] = Path(downloaded_path).stat().st_size
            result['success'] = True

            # 通知完成
            progress_queue.put({
                'type': 'completed',
                'filename': filename,
                'size': result['size'],
                'elapsed_time': result['elapsed_time'],
                'position': position
            })

        except Exception as e:
            result['error'] = str(e)
            progress_queue.put({
                'type': 'failed',
                'filename': filename,
                'error': str(e),
                'position': position
            })

        return result
    
    def download_missing_files_parallel(self, missing_files: List[str]) -> bool:
        """并行下载缺失的文件"""
        if not missing_files:
            print("所有文件都已存在且完整，无需下载。")
            return True

        print(f"准备并行下载 {len(missing_files)} 个文件 (最大并发数: {self.max_workers})")
        for file in missing_files:
            print(f"  - {file}")

        # 初始化统计
        self.download_stats['total_files'] = len(missing_files)

        # 创建进度队列
        progress_queue = queue.Queue()

        # 创建总体进度条
        main_pbar = tqdm(
            total=len(missing_files),
            desc="总体进度",
            unit="文件",
            position=0,
            leave=True
        )

        # 创建活动下载进度条字典
        active_downloads = {}
        success_count = 0
        failed_files = []

        # 创建状态显示进度条
        status_pbar = tqdm(
            desc="状态",
            position=1,
            leave=False,
            bar_format='{desc}: {postfix}'
        )
        
        # 启动进度监控线程
        def progress_monitor():
            while True:
                try:
                    progress_info = progress_queue.get(timeout=1)

                    if progress_info['type'] == 'started':
                        filename = progress_info['filename']
                        position = progress_info['position']

                        # 为新的下载创建进度条
                        file_pbar = tqdm(
                            desc=f"下载: {filename[:25]}...",
                            position=position + 2,
                            leave=False,
                            bar_format='{desc}: {postfix}'
                        )
                        active_downloads[filename] = file_pbar

                        status_pbar.set_postfix({
                            "活跃": len(active_downloads),
                            "当前": filename[:20] + "..."
                        })

                    elif progress_info['type'] == 'completed':
                        nonlocal success_count
                        success_count += 1
                        filename = progress_info['filename']
                        file_size_mb = progress_info['size'] / (1024 * 1024)
                        elapsed_time = progress_info['elapsed_time']

                        # 更新文件进度条
                        if filename in active_downloads:
                            active_downloads[filename].set_postfix({
                                "状态": "✓ 完成",
                                "大小": f"{file_size_mb:.1f}MB",
                                "速度": f"{file_size_mb/elapsed_time:.1f}MB/s" if elapsed_time > 0 else "N/A"
                            })
                            active_downloads[filename].close()
                            del active_downloads[filename]

                        # 更新总体进度
                        main_pbar.set_postfix({
                            "成功": f"{success_count}/{len(missing_files)}",
                            "活跃": len(active_downloads),
                            "最新": filename[:15] + "..."
                        })
                        main_pbar.update(1)

                    elif progress_info['type'] == 'failed':
                        filename = progress_info['filename']
                        failed_files.append(filename)

                        # 更新文件进度条
                        if filename in active_downloads:
                            active_downloads[filename].set_postfix({
                                "状态": "✗ 失败",
                                "错误": progress_info['error'][:20] + "..."
                            })
                            active_downloads[filename].close()
                            del active_downloads[filename]

                        # 更新总体进度
                        main_pbar.set_postfix({
                            "失败": len(failed_files),
                            "活跃": len(active_downloads),
                            "错误": filename[:15] + "..."
                        })
                        main_pbar.update(1)

                    progress_queue.task_done()

                except queue.Empty:
                    continue
                except:
                    break
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=progress_monitor, daemon=True)
        monitor_thread.start()
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有下载任务
            future_to_filename = {
                executor.submit(self.download_single_file_with_progress, filename, progress_queue, i): filename
                for i, filename in enumerate(missing_files)
            }

            # 等待所有任务完成
            for future in as_completed(future_to_filename):
                filename = future_to_filename[future]
                try:
                    result = future.result()
                    status_pbar.set_description(f"处理完成: {filename[:20]}...")
                except Exception as e:
                    print(f"\n处理文件 {filename} 时发生异常: {e}")

        # 关闭所有进度条
        status_pbar.close()
        for pbar in active_downloads.values():
            pbar.close()
        main_pbar.close()
        
        # 显示最终结果
        print(f"\n下载完成!")
        print(f"成功: {success_count}/{len(missing_files)}")
        if failed_files:
            print(f"失败的文件:")
            for failed_file in failed_files:
                print(f"  - {failed_file}")
        
        return success_count == len(missing_files)
    
    def run(self) -> bool:
        """执行增量下载"""
        print("=" * 60)
        print(f"开始高级增量下载 {self.repo_id}")
        print(f"目标目录: {self.local_dir}")
        print(f"最大并发数: {self.max_workers}")
        print("=" * 60)
        
        # 获取远程文件列表
        remote_files = self.get_remote_files()
        if not remote_files:
            print("无法获取远程文件列表，退出。")
            return False
        
        # 获取本地文件列表
        local_files = self.get_local_files()
        
        # 识别缺失文件
        missing_files = self.identify_missing_files(remote_files, local_files)
        
        # 并行下载缺失文件
        return self.download_missing_files_parallel(missing_files)


def main():
    # 配置参数
    repo_id = "wangxiangyu0814/TravelUAV_env"
    local_dir = "/home/<USER>/TravelUAV/envs/TravelUAV_env"
    max_workers = 3  # 最大并发下载数
    
    # 创建下载器并执行
    downloader = AdvancedDownloader(repo_id, local_dir, max_workers=max_workers)
    success = downloader.run()
    
    if success:
        print("\n🎉 增量下载完成!")
    else:
        print("\n❌ 下载过程中出现错误")


if __name__ == "__main__":
    main()
