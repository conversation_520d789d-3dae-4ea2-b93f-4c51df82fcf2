#!/bin/bash

# TravelUAV 模型下载脚本
# 使用国内镜像加速下载

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查环境
check_environment() {
    print_message $BLUE "🔧 检查下载环境..."
    
    # 检查huggingface-cli
    if ! command -v huggingface-cli &> /dev/null; then
        print_message $YELLOW "⚠️  huggingface-cli 未安装，正在安装..."
        pip install huggingface_hub -i https://pypi.tuna.tsinghua.edu.cn/simple/
    fi
    
    # 检查wget
    if ! command -v wget &> /dev/null; then
        print_message $RED "❌ wget 未安装，请先安装 wget"
        exit 1
    fi
    
    print_message $GREEN "✅ 环境检查完成"
}

# 创建目录结构
create_directories() {
    print_message $BLUE "📁 创建目录结构..."
    
    mkdir -p Model/LLaMA-UAV/work_dirs
    mkdir -p Model/LLaMA-UAV/model_zoo/LAVIS
    mkdir -p Model/LLaMA-UAV/llamavid/processor
    mkdir -p src/model_wrapper/utils/GroundingDINO/weights
    
    print_message $GREEN "✅ 目录创建完成"
}

# 下载LLaMA-UAV主模型
download_llama_uav() {
    print_message $BLUE "📥 下载LLaMA-UAV主模型 (约14GB)..."
    
    local model_dir="Model/LLaMA-UAV/work_dirs/llama-vid-7b-pretrain-224-uav-full-data-lora32"
    
    if [ -d "$model_dir" ] && [ "$(ls -A $model_dir)" ]; then
        print_message $YELLOW "⚠️  模型已存在，跳过下载"
        return 0
    fi
    
    export HF_ENDPOINT=https://hf-mirror.com
    huggingface-cli download wangxiangyu0814/llama-uav-7b \
        --local-dir "$model_dir" \
        --resume-download
    
    print_message $GREEN "✅ LLaMA-UAV主模型下载完成"
}

# 下载Vicuna基础模型
download_vicuna() {
    print_message $BLUE "📥 下载Vicuna-7B基础模型 (约13GB)..."
    
    local model_dir="Model/LLaMA-UAV/model_zoo/vicuna-7b-v1.5"
    
    if [ -d "$model_dir" ] && [ "$(ls -A $model_dir)" ]; then
        print_message $YELLOW "⚠️  Vicuna模型已存在，跳过下载"
        return 0
    fi
    
    export HF_ENDPOINT=https://hf-mirror.com
    huggingface-cli download lmsys/vicuna-7b-v1.5 \
        --local-dir "$model_dir" \
        --resume-download
    
    print_message $GREEN "✅ Vicuna-7B模型下载完成"
}

# 下载EVA-ViT-G视觉编码器
download_eva_vit() {
    print_message $BLUE "📥 下载EVA-ViT-G视觉编码器 (约1.7GB)..."
    
    local model_file="Model/LLaMA-UAV/model_zoo/LAVIS/eva_vit_g.pth"
    
    if [ -f "$model_file" ]; then
        print_message $YELLOW "⚠️  EVA-ViT-G已存在，跳过下载"
        return 0
    fi
    
    wget -c https://storage.googleapis.com/sfr-vision-language-research/LAVIS/models/BLIP2/eva_vit_g.pth \
        -O "$model_file"
    
    print_message $GREEN "✅ EVA-ViT-G下载完成"
}

# 下载轨迹完成模型
download_trajectory_model() {
    print_message $BLUE "📥 下载轨迹完成模型..."
    
    local model_dir="Model/LLaMA-UAV/work_dirs/traj_predictor_bs_128_drop_0.1_lr_5e-4"
    
    if [ -d "$model_dir" ] && [ "$(ls -A $model_dir)" ]; then
        print_message $YELLOW "⚠️  轨迹模型已存在，跳过下载"
        return 0
    fi
    
    export HF_ENDPOINT=https://hf-mirror.com
    huggingface-cli download wangxiangyu0814/traveluav-traj-model \
        --local-dir "$model_dir" \
        --resume-download
    
    print_message $GREEN "✅ 轨迹完成模型下载完成"
}

# 下载CLIP图像处理器
download_clip_processor() {
    print_message $BLUE "📥 下载CLIP图像处理器..."
    
    local processor_dir="Model/LLaMA-UAV/llamavid/processor/clip-patch14-224"
    
    if [ -d "$processor_dir" ] && [ "$(ls -A $processor_dir)" ]; then
        print_message $YELLOW "⚠️  CLIP处理器已存在，跳过下载"
        return 0
    fi
    
    export HF_ENDPOINT=https://hf-mirror.com
    huggingface-cli download openai/clip-vit-large-patch14-336 \
        --local-dir "$processor_dir" \
        --resume-download
    
    print_message $GREEN "✅ CLIP图像处理器下载完成"
}

# 下载GroundingDINO权重
download_groundingdino() {
    print_message $BLUE "📥 下载GroundingDINO权重..."
    
    local weight_file="src/model_wrapper/utils/GroundingDINO/weights/groundingdino_swint_ogc.pth"
    
    if [ -f "$weight_file" ]; then
        print_message $YELLOW "⚠️  GroundingDINO权重已存在，跳过下载"
        return 0
    fi
    
    mkdir -p "$(dirname "$weight_file")"
    wget -c https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth \
        -O "$weight_file"
    
    print_message $GREEN "✅ GroundingDINO权重下载完成"
}

# 显示下载菜单
show_download_menu() {
    print_message $BLUE "📥 TravelUAV 模型下载菜单"
    echo "=================================="
    echo "1. 🚀 下载所有模型 (推荐)"
    echo "2. 📦 下载LLaMA-UAV主模型 (~14GB)"
    echo "3. 🦙 下载Vicuna-7B基础模型 (~13GB)"
    echo "4. 👁️  下载EVA-ViT-G视觉编码器 (~1.7GB)"
    echo "5. 🛣️  下载轨迹完成模型"
    echo "6. 🖼️  下载CLIP图像处理器"
    echo "7. 🎯 下载GroundingDINO权重"
    echo "8. 📊 检查已下载模型"
    echo "9. 🧹 清理下载缓存"
    echo "0. 退出"
    echo "=================================="
}

# 下载所有模型
download_all_models() {
    print_message $GREEN "🚀 开始下载所有模型..."
    
    local start_time=$(date +%s)
    
    create_directories
    download_llama_uav
    download_vicuna
    download_eva_vit
    download_trajectory_model
    download_clip_processor
    download_groundingdino
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_message $GREEN "🎉 所有模型下载完成！"
    print_message $BLUE "⏱️  总用时: $((duration / 60))分$((duration % 60))秒"
}

# 检查已下载模型
check_downloaded_models() {
    print_message $BLUE "📊 检查已下载模型状态..."
    
    local models=(
        "Model/LLaMA-UAV/work_dirs/llama-vid-7b-pretrain-224-uav-full-data-lora32:LLaMA-UAV主模型"
        "Model/LLaMA-UAV/model_zoo/vicuna-7b-v1.5:Vicuna-7B基础模型"
        "Model/LLaMA-UAV/model_zoo/LAVIS/eva_vit_g.pth:EVA-ViT-G视觉编码器"
        "Model/LLaMA-UAV/work_dirs/traj_predictor_bs_128_drop_0.1_lr_5e-4:轨迹完成模型"
        "Model/LLaMA-UAV/llamavid/processor/clip-patch14-224:CLIP图像处理器"
        "src/model_wrapper/utils/GroundingDINO/weights/groundingdino_swint_ogc.pth:GroundingDINO权重"
    )
    
    echo "模型状态检查结果："
    echo "===================="
    
    for model_info in "${models[@]}"; do
        IFS=':' read -r path name <<< "$model_info"
        
        if [ -e "$path" ] && [ "$(ls -A "$path" 2>/dev/null)" ]; then
            local size=""
            if [ -f "$path" ]; then
                size=" ($(du -h "$path" | cut -f1))"
            elif [ -d "$path" ]; then
                size=" ($(du -sh "$path" | cut -f1))"
            fi
            print_message $GREEN "✅ $name$size"
        else
            print_message $RED "❌ $name - 未下载"
        fi
    done
    
    echo "===================="
    
    # 计算总大小
    local total_size=$(du -sh Model/ 2>/dev/null | cut -f1 || echo "0")
    print_message $BLUE "📦 模型总大小: $total_size"
}

# 清理下载缓存
clean_cache() {
    print_message $YELLOW "🧹 清理下载缓存..."
    
    # 清理HuggingFace缓存
    if [ -d "/home/<USER>/.cache/huggingface" ]; then
        local cache_size=$(du -sh /home/<USER>/.cache/huggingface | cut -f1)
        print_message $BLUE "HuggingFace缓存大小: $cache_size"
        
        read -p "是否清理HuggingFace缓存? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            rm -rf /home/<USER>/.cache/huggingface/*
            print_message $GREEN "✅ HuggingFace缓存已清理"
        fi
    fi
    
    # 清理wget临时文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "*.part" -delete 2>/dev/null || true
    
    print_message $GREEN "✅ 缓存清理完成"
}

# 主函数
main() {
    # 检查是否在正确目录
    if [ ! -f "setup_env.sh" ]; then
        print_message $RED "❌ 请在TravelUAV项目根目录运行此脚本"
        exit 1
    fi
    
    # 激活环境
    source setup_env.sh
    
    # 检查环境
    check_environment
    
    # 如果有参数，直接执行对应功能
    if [ $# -gt 0 ]; then
        case $1 in
            "all") download_all_models ;;
            "llama") download_llama_uav ;;
            "vicuna") download_vicuna ;;
            "eva") download_eva_vit ;;
            "traj") download_trajectory_model ;;
            "clip") download_clip_processor ;;
            "grounding") download_groundingdino ;;
            "check") check_downloaded_models ;;
            "clean") clean_cache ;;
            *) print_message $RED "❌ 未知参数: $1" ;;
        esac
        return
    fi
    
    # 交互式菜单
    while true; do
        echo ""
        show_download_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1) download_all_models ;;
            2) download_llama_uav ;;
            3) download_vicuna ;;
            4) download_eva_vit ;;
            5) download_trajectory_model ;;
            6) download_clip_processor ;;
            7) download_groundingdino ;;
            8) check_downloaded_models ;;
            9) clean_cache ;;
            0) 
                print_message $GREEN "👋 下载完成！"
                exit 0
                ;;
            *)
                print_message $RED "❌ 无效选择，请输入 0-9"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 运行主函数
main "$@"
