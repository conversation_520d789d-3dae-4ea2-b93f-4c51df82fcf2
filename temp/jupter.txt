(base) zhaobaining@aiotllm-PR4910P2:~/TravelUAV$ source ~/miniconda3/etc/profile.d/conda.sh
conda activate llamauav
nohup jupyter lab --no-browser --ip=0.0.0.0 --port=8888 > logs/jupyter.log 2>&1 &
# 或（更轻量）
# nohup jupyter notebook --no-browser --ip=0.0.0.0 --port=8888 > logs/jupyter.log 2>&1 &
echo $! > jupyter.pid
[1] 670854
(llamauav) zhaobaining@aiotllm-PR4910P2:~/TravelUAV$ tail -f logs/jupyter.log
[I 2025-09-07 15:58:15.985 ServerApp]     http://127.0.0.1:8890/lab?token=647aabd6cd62bab2f6cc279d8e4c04ff9b1056ee6205196f
[I 2025-09-07 15:58:15.985 ServerApp] Use Control-C to stop this server and shut down all kernels (twice to skip confirmation).
[C 2025-09-07 15:58:15.989 ServerApp] 
    
    To access the server, open this file in a browser:
        file:///home/<USER>/.local/share/jupyter/runtime/jpserver-670854-open.html
    Or copy and paste one of these URLs:
        http://aiotllm-PR4910P2:8890/lab?token=647aabd6cd62bab2f6cc279d8e4c04ff9b1056ee6205196f
        http://127.0.0.1:8890/lab?token=647aabd6cd62bab2f6cc279d8e4c04ff9b1056ee6205196f
[I 2025-09-07 15:58:16.020 ServerApp] Skipped non-installed server(s): basedpyright, bash-language-server, dockerfile-language-server-nodejs, javascript-typescript-langserver, jedi-language-server, julia-language-server, pyrefly, pyright, python-language-server, python-lsp-server, r-languageserver, sql-language-server, texlab, typescript-language-server, unified-language-server, vscode-css-languageserver-bin, vscode-html-languageserver-bin, vscode-json-languageserver-bin, yaml-language-server


source ~/miniconda3/etc/profile.d/conda.sh
conda activate llamauav
nohup jupyter lab --no-browser --ip=0.0.0.0 --port=8888 > logs/jupyter.log 2>&1 &
# 或（更轻量）
# nohup jupyter notebook --no-browser --ip=0.0.0.0 --port=8888 > logs/jupyter.log 2>&1 &
echo $! > jupyter.pid

查看日志/关闭：
tail -f logs/jupyter.log
kill $(cat jupyter.pid)




GitHub Copilot: 我将读取现有的环境安装脚本以便在启动 Jupyter 的后台脚本里复用。 我将查看 requirements 以确定 Jupyter 是否在依赖里。 我先给出最稳妥的几种后台常驻方式，你任选一种；需要我帮你在仓库里新增脚本再说一声即可。

## 方式 1：nohup 后台（最简单）
进入项目根目录后：
```bash
source ~/miniconda3/etc/profile.d/conda.sh
conda activate llamauav
nohup jupyter lab --no-browser --ip=0.0.0.0 --port=8888 > logs/jupyter.log 2>&1 &
# 或（更轻量）
# nohup jupyter notebook --no-browser --ip=0.0.0.0 --port=8888 > logs/jupyter.log 2>&1 &
echo $! > jupyter.pid
```
查看日志/关闭：
```bash
tail -f logs/jupyter.log
kill $(cat jupyter.pid)
```

## 方式 2：tmux（可随时重新接入）
```bash
tmux new -s jup
source ~/miniconda3/etc/profile.d/conda.sh
conda activate llamauav
jupyter lab --no-browser --ip=0.0.0.0 --port=8888
# 按 Ctrl+B 然后 D 分离
tmux attach -t jup   # 回来
```

## 方式 3：systemd 用户服务（登录自动启动，最持久）
创建文件：
~/.config/systemd/user/jupyter.service
```
[Unit]
Description=Jupyter Lab
After=network.target

[Service]
Type=simple
ExecStart=/bin/bash -c 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate llamauav && cd /home/<USER>/TravelUAV && jupyter lab --no-browser --ip=0.0.0.0 --port=8888'
Restart=on-failure

[Install]
WantedBy=default.target
```
启用：
```bash
systemctl --user daemon-reload
systemctl --user enable --now jupyter
systemctl --user status jupyter
journalctl --user -u jupyter -f
```

## 方式 4：生成一个脚本（如果要我加文件请说）
内容示例（jupyter_background.sh）：
```bash
#!/bin/bash
set -e
source ~/miniconda3/etc/profile.d/conda.sh
conda activate llamauav
source /home/<USER>/TravelUAV/.env 2>/dev/null || true
mkdir -p logs
PORT=${1:-8888}
nohup jupyter lab --no-browser --ip=0.0.0.0 --port=$PORT > logs/jupyter.log 2>&1 &
echo $! > jupyter.pid
echo "Started Jupyter Lab on port $PORT (PID $(cat jupyter.pid)). Log: logs/jupyter.log"
```

## 安全与访问
- 远程访问建议先设置密码：
```bash
jupyter notebook password
```
- 或生成配置：
```bash
jupyter notebook --generate-config
# 编辑 ~/.jupyter/jupyter_notebook_config.py 中设：
# c.NotebookApp.password = 'sha1:...'
```
- 需要反代可之后再加 Nginx。

## 未安装 Jupyter？
在该 conda 环境中安装：
```bash
pip install jupyterlab
```
（可追加到 requirement.txt 或单独做 dev 依赖）

## Token 查看
日志里有形如 ?token=xxxx，或：
```bash
jupyter notebook list
```
