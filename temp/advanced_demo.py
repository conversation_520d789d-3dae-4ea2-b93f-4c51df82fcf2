#!/usr/bin/env python3
"""
TravelUAV 高级可视化演示界面
包含轨迹可视化、模型推理、实时监控等功能
"""

import gradio as gr
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
import json
import os
import sys
from pathlib import Path
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class TravelUAVDemo:
    def __init__(self):
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        self.model_loaded = False
        self.trajectory_data = []
        
    def generate_sample_trajectory(self, instruction="飞到目标点"):
        """生成示例轨迹数据"""
        np.random.seed(42)
        
        # 生成3D轨迹点
        t = np.linspace(0, 10, 50)
        x = t * 2 + np.random.normal(0, 0.5, len(t))
        y = np.sin(t) * 3 + np.random.normal(0, 0.3, len(t))
        z = np.cos(t) * 2 + 5 + np.random.normal(0, 0.2, len(t))
        
        trajectory = []
        for i in range(len(t)):
            trajectory.append({
                'timestamp': i * 0.2,
                'position': [float(x[i]), float(y[i]), float(z[i])],
                'rotation': [0, 0, float(np.arctan2(y[i+1]-y[i] if i<len(t)-1 else 0, 
                                                   x[i+1]-x[i] if i<len(t)-1 else 1))],
                'velocity': [1.0, 0.5, 0.1],
                'instruction': instruction
            })
        
        return trajectory
    
    def create_3d_trajectory_plot(self, trajectory_data):
        """创建3D轨迹可视化"""
        if not trajectory_data:
            trajectory_data = self.generate_sample_trajectory()
        
        positions = np.array([point['position'] for point in trajectory_data])
        
        fig = go.Figure()
        
        # 添加轨迹线
        fig.add_trace(go.Scatter3d(
            x=positions[:, 0],
            y=positions[:, 1],
            z=positions[:, 2],
            mode='lines+markers',
            line=dict(color='blue', width=4),
            marker=dict(size=3, color='lightblue'),
            name='飞行轨迹'
        ))
        
        # 标记起点和终点
        fig.add_trace(go.Scatter3d(
            x=[positions[0, 0]],
            y=[positions[0, 1]],
            z=[positions[0, 2]],
            mode='markers',
            marker=dict(size=10, color='green', symbol='diamond'),
            name='起点'
        ))
        
        fig.add_trace(go.Scatter3d(
            x=[positions[-1, 0]],
            y=[positions[-1, 1]],
            z=[positions[-1, 2]],
            mode='markers',
            marker=dict(size=10, color='red', symbol='square'),
            name='终点'
        ))
        
        fig.update_layout(
            title='无人机3D飞行轨迹',
            scene=dict(
                xaxis_title='X (米)',
                yaxis_title='Y (米)',
                zaxis_title='Z (米)',
                camera=dict(eye=dict(x=1.5, y=1.5, z=1.5))
            ),
            width=800,
            height=600
        )
        
        return fig
    
    def create_2d_trajectory_plot(self, trajectory_data):
        """创建2D轨迹可视化"""
        if not trajectory_data:
            trajectory_data = self.generate_sample_trajectory()
        
        positions = np.array([point['position'] for point in trajectory_data])
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # XY平面视图
        ax1.plot(positions[:, 0], positions[:, 1], 'b-', linewidth=2, label='轨迹')
        ax1.scatter(positions[0, 0], positions[0, 1], color='green', s=100, 
                   marker='o', label='起点', zorder=5)
        ax1.scatter(positions[-1, 0], positions[-1, 1], color='red', s=100, 
                   marker='s', label='终点', zorder=5)
        ax1.set_xlabel('X (米)')
        ax1.set_ylabel('Y (米)')
        ax1.set_title('俯视图 (XY平面)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.axis('equal')
        
        # 高度变化图
        timestamps = [point['timestamp'] for point in trajectory_data]
        ax2.plot(timestamps, positions[:, 2], 'r-', linewidth=2, label='高度')
        ax2.fill_between(timestamps, positions[:, 2], alpha=0.3, color='red')
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('高度 (米)')
        ax2.set_title('高度变化')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        return fig
    
    def simulate_object_detection(self, image, prompt):
        """模拟目标检测"""
        if image is None:
            # 创建示例图像
            img = Image.new('RGB', (640, 480), color='lightblue')
            draw = ImageDraw.Draw(img)
            
            # 绘制一些示例对象
            draw.rectangle([100, 100, 200, 200], outline='red', width=3)
            draw.rectangle([300, 150, 450, 300], outline='green', width=3)
            draw.rectangle([500, 50, 600, 150], outline='blue', width=3)
            
            # 添加标签
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            draw.text((105, 80), "建筑物", fill='red', font=font)
            draw.text((305, 130), "车辆", fill='green', font=font)
            draw.text((505, 30), "树木", fill='blue', font=font)
            
            image = img
        
        # 模拟检测结果
        detection_results = {
            "检测到的对象": ["建筑物", "车辆", "树木"],
            "置信度": [0.95, 0.87, 0.92],
            "边界框": [
                {"x": 100, "y": 100, "w": 100, "h": 100},
                {"x": 300, "y": 150, "w": 150, "h": 150},
                {"x": 500, "y": 50, "w": 100, "h": 100}
            ],
            "处理时间": "0.15秒"
        }
        
        result_text = f"""
🎯 目标检测结果:

📝 输入提示: {prompt}

🔍 检测结果:
"""
        
        for i, (obj, conf) in enumerate(zip(detection_results["检测到的对象"], detection_results["置信度"])):
            bbox = detection_results["边界框"][i]
            result_text += f"- {obj}: {conf:.2%} 置信度 (位置: {bbox['x']}, {bbox['y']})\n"
        
        result_text += f"\n⏱️ 处理时间: {detection_results['处理时间']}"
        
        return image, result_text
    
    def generate_navigation_plan(self, instruction, current_pos, target_pos):
        """生成导航计划"""
        plan = f"""
🧭 导航计划生成

📍 当前位置: {current_pos}
🎯 目标位置: {target_pos}
📝 指令: {instruction}

📋 执行计划:
1. 🚁 起飞到安全高度 (5米)
2. 🔍 扫描周围环境，识别障碍物
3. 📐 计算最优路径
4. 🛣️ 沿规划路径飞行
5. 🎯 接近目标区域
6. 🛬 安全降落

⚠️ 安全检查:
✅ 电池电量充足 (85%)
✅ GPS信号良好
✅ 天气条件适宜
✅ 飞行区域许可

📊 预计参数:
- 飞行距离: {np.linalg.norm(np.array(target_pos) - np.array(current_pos)):.1f}米
- 预计时间: {np.linalg.norm(np.array(target_pos) - np.array(current_pos))/5:.1f}分钟
- 最大高度: 10米
- 平均速度: 5米/秒
"""
        return plan

def create_advanced_demo():
    demo_instance = TravelUAVDemo()
    
    with gr.Blocks(title="TravelUAV Advanced Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🚁 TravelUAV 高级演示系统")
        gr.Markdown("基于LLaMA的无人机视觉语言导航 - 完整功能演示")
        
        with gr.Tabs():
            # 轨迹可视化标签页
            with gr.TabItem("📈 轨迹可视化"):
                gr.Markdown("## 3D飞行轨迹可视化")
                
                with gr.Row():
                    with gr.Column():
                        instruction_input = gr.Textbox(
                            label="导航指令",
                            value="飞到红色建筑物附近",
                            lines=2
                        )
                        generate_traj_btn = gr.Button("生成轨迹", variant="primary")
                    
                    with gr.Column():
                        traj_info = gr.Textbox(
                            label="轨迹信息",
                            lines=5,
                            value="点击'生成轨迹'开始..."
                        )
                
                # 3D轨迹图
                trajectory_3d = gr.Plot(label="3D轨迹可视化")
                
                # 2D轨迹图
                trajectory_2d = gr.Plot(label="2D轨迹分析")
                
                def update_trajectory(instruction):
                    traj_data = demo_instance.generate_sample_trajectory(instruction)
                    fig_3d = demo_instance.create_3d_trajectory_plot(traj_data)
                    fig_2d = demo_instance.create_2d_trajectory_plot(traj_data)
                    
                    info = f"""
轨迹生成完成！

📊 轨迹统计:
- 总点数: {len(traj_data)}
- 飞行时间: {traj_data[-1]['timestamp']:.1f}秒
- 起始位置: {traj_data[0]['position']}
- 结束位置: {traj_data[-1]['position']}
"""
                    return fig_3d, fig_2d, info
                
                generate_traj_btn.click(
                    update_trajectory,
                    inputs=[instruction_input],
                    outputs=[trajectory_3d, trajectory_2d, traj_info]
                )
            
            # 目标检测标签页
            with gr.TabItem("🎯 目标检测"):
                gr.Markdown("## GroundingDINO 目标检测演示")
                
                with gr.Row():
                    with gr.Column():
                        detection_image = gr.Image(
                            label="输入图像",
                            type="pil"
                        )
                        detection_prompt = gr.Textbox(
                            label="检测提示",
                            value="building . car . tree",
                            lines=2
                        )
                        detect_btn = gr.Button("开始检测", variant="primary")
                    
                    with gr.Column():
                        detection_result_image = gr.Image(
                            label="检测结果图像"
                        )
                        detection_result_text = gr.Textbox(
                            label="检测结果",
                            lines=10
                        )
                
                detect_btn.click(
                    demo_instance.simulate_object_detection,
                    inputs=[detection_image, detection_prompt],
                    outputs=[detection_result_image, detection_result_text]
                )
            
            # 导航规划标签页
            with gr.TabItem("🧭 导航规划"):
                gr.Markdown("## 智能导航路径规划")
                
                with gr.Row():
                    with gr.Column():
                        nav_instruction = gr.Textbox(
                            label="导航指令",
                            value="飞到停车场并寻找空位",
                            lines=3
                        )
                        current_position = gr.Textbox(
                            label="当前位置 [x, y, z]",
                            value="[0, 0, 0]"
                        )
                        target_position = gr.Textbox(
                            label="目标位置 [x, y, z]",
                            value="[20, 15, 5]"
                        )
                        plan_btn = gr.Button("生成导航计划", variant="primary")
                    
                    with gr.Column():
                        navigation_plan = gr.Textbox(
                            label="导航计划",
                            lines=20
                        )
                
                def create_nav_plan(instruction, current, target):
                    try:
                        current_pos = eval(current)
                        target_pos = eval(target)
                        return demo_instance.generate_navigation_plan(instruction, current_pos, target_pos)
                    except:
                        return "❌ 位置格式错误，请使用 [x, y, z] 格式"
                
                plan_btn.click(
                    create_nav_plan,
                    inputs=[nav_instruction, current_position, target_position],
                    outputs=[navigation_plan]
                )
            
            # 系统监控标签页
            with gr.TabItem("📊 系统监控"):
                gr.Markdown("## 实时系统状态监控")
                
                with gr.Row():
                    with gr.Column():
                        refresh_btn = gr.Button("刷新状态", variant="secondary")
                        auto_refresh = gr.Checkbox(label="自动刷新", value=False)
                    
                    with gr.Column():
                        system_time = gr.Textbox(
                            label="系统时间",
                            value=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        )
                
                # 系统状态显示
                gpu_status = gr.Textbox(label="GPU状态", lines=5)
                memory_status = gr.Textbox(label="内存状态", lines=3)
                model_status = gr.Textbox(label="模型状态", lines=3)
                
                def update_system_status():
                    gpu_info = "GPU信息获取中..."
                    if torch.cuda.is_available():
                        gpu_info = f"""
GPU: {torch.cuda.get_device_name(0)}
显存使用: {torch.cuda.memory_allocated(0)/1024**3:.1f}GB / {torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB
显存利用率: {torch.cuda.memory_allocated(0)/torch.cuda.get_device_properties(0).total_memory*100:.1f}%
"""
                    
                    import psutil
                    memory_info = f"""
内存使用: {psutil.virtual_memory().used/1024**3:.1f}GB / {psutil.virtual_memory().total/1024**3:.1f}GB
内存利用率: {psutil.virtual_memory().percent:.1f}%
"""
                    
                    model_info = f"""
模型状态: {'已加载' if demo_instance.model_loaded else '未加载'}
设备: {demo_instance.device}
PyTorch版本: {torch.__version__}
"""
                    
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    return gpu_info, memory_info, model_info, current_time
                
                refresh_btn.click(
                    update_system_status,
                    outputs=[gpu_status, memory_status, model_status, system_time]
                )
        
        # 初始化显示
        demo.load(
            lambda: demo_instance.create_3d_trajectory_plot([]),
            outputs=[trajectory_3d]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_advanced_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,  # 使用不同端口避免冲突
        share=False,
        debug=True
    )
