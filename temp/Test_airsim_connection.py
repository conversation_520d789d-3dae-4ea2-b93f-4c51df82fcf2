#!/usr/bin/env python3
"""
AirSim连接测试脚本
"""

import airsim
import time
import sys

def test_airsim_connection():
    """测试AirSim连接"""
    print("=== AirSim连接测试 ===")
    
    try:
        # 连接到AirSim
        client = airsim.MultirotorClient()
        client.confirmConnection()
        print("✅ AirSim连接成功")
        
        # 获取无人机状态
        state = client.getMultirotorState()
        pos = state.kinematics_estimated.position
        print(f"✅ 无人机位置: x={pos.x_val:.2f}, y={pos.y_val:.2f}, z={pos.z_val:.2f}")
        
        # 检查API控制
        client.enableApiControl(True)
        print("✅ API控制启用成功")
        
        # 获取图像（测试相机）
        responses = client.simGetImages([
            airsim.ImageRequest("FrontCamera", airsim.ImageType.Scene, False, False)
        ])
        
        if responses and len(responses) > 0:
            print(f"✅ 相机图像获取成功，图像大小: {len(responses[0].image_data_uint8)} bytes")
        else:
            print("⚠️  相机图像获取失败")
        
        # 禁用API控制
        client.enableApiControl(False)
        
        return True
        
    except Exception as e:
        print(f"❌ AirSim连接失败: {e}")
        print("请确保AirSim服务器正在运行")
        return False

def main():
    """主函数"""
    print("TravelUAV AirSim连接测试")
    print("=" * 40)
    
    success = test_airsim_connection()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 AirSim测试通过！")
        print("💡 您可以开始使用AirSim进行无人机仿真")
    else:
        print("❌ AirSim测试失败")
        print("📝 请检查:")
        print("  1. AirSim服务器是否正在运行")
        print("  2. 端口配置是否正确")
        print("  3. 环境文件是否正确安装")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
