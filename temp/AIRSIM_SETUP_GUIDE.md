# 🚁 TravelUAV AirSim环境配置指南

## 📋 概述

TravelUAV使用AirSim仿真环境进行无人机导航训练和测试。本指南将帮助您完成AirSim环境的下载、配置和测试。

## 🎯 环境文件下载

### 1. 从HuggingFace数据集下载

**数据集地址：** https://huggingface.co/datasets/wangxiangyu0814/TravelUAV_env

**下载方法：**

```bash
# 方法1: 使用git-lfs下载（推荐）
cd /home/<USER>/TravelUAV
mkdir -p envs
cd envs

# 克隆环境数据集
git lfs clone https://huggingface.co/datasets/wangxiangyu0814/TravelUAV_env

# 方法2: 使用huggingface-cli下载
huggingface-cli download wangxiangyu0814/TravelUAV_env --local-dir ./TravelUAV_env --repo-type dataset
```

### 2. 环境文件结构

下载完成后，您应该有以下目录结构：

```
envs/
├── closeloop_envs/          # 闭环测试环境
│   ├── NYCEnvironmentMegapa.sh
│   ├── TropicalIsland.sh
│   ├── NewYorkCity.sh
│   └── ...
├── carla_town_envs/         # Carla城镇环境
│   ├── Town01.sh
│   ├── Town02.sh
│   └── ...
└── extra_envs/              # 额外环境
    ├── UrbanEnvironment.sh
    └── ...
```

## 🔧 配置AirSim服务器

### 1. 更新环境路径配置

编辑 `airsim_plugin/AirVLNSimulatorServerTool.py` 中的路径配置：

```python
# 在第679行附近，更新root_path默认值
parser.add_argument(
    "--root_path",
    type=str,
    default="/home/<USER>/TravelUAV/envs",  # 更新为您的环境路径
    help='root dir for env path'
)
```

### 2. 环境映射配置

确认 `env_exec_path_dict` 配置正确（第241行附近）：

```python
env_exec_path_dict = {
    "NYCEnvironmentMegapa": {
        'bash_name': 'NYCEnvironmentMegapa',
        'exec_path': './closeloop_envs',
    },
    "TropicalIsland": {
        'bash_name': 'TropicalIsland', 
        'exec_path': './closeloop_envs',
    },
    # ... 其他环境配置
}
```

## 🚀 启动AirSim服务器

### 1. 基本启动命令

```bash
cd /home/<USER>/TravelUAV/airsim_plugin

# 启动服务器（使用默认配置）
python AirVLNSimulatorServerTool.py \
    --port 30000 \
    --root_path /home/<USER>/TravelUAV/envs \
    --gpus 0,1,2,3

# 或使用我们的启动脚本
cd /home/<USER>/TravelUAV
./start_traveluav.sh airsim
```

### 2. 参数说明

- `--port`: 服务器端口（默认30000）
- `--root_path`: 环境文件根目录
- `--gpus`: 可用GPU列表

### 3. 测试连接

```bash
# 测试AirSim连接
python -c "
import airsim
client = airsim.MultirotorClient()
client.confirmConnection()
print('AirSim连接成功！')
"
```

## 🏗️ 环境文件权限设置

确保环境脚本有执行权限：

```bash
cd /home/<USER>/TravelUAV/envs

# 给所有.sh文件添加执行权限
find . -name "*.sh" -exec chmod +x {} \;

# 检查权限
ls -la closeloop_envs/*.sh
```

## 🔍 故障排除

### 1. 常见问题

**问题1: 环境文件下载失败**
```bash
# 解决方案: 使用断点续传
git lfs pull
# 或重新克隆
rm -rf TravelUAV_env
git lfs clone https://huggingface.co/datasets/wangxiangyu0814/TravelUAV_env
```

**问题2: 权限被拒绝**
```bash
# 解决方案: 检查文件权限
chmod +x envs/closeloop_envs/*.sh
```

**问题3: 端口被占用**
```bash
# 解决方案: 检查端口使用情况
netstat -tulpn | grep 30000
# 杀死占用进程
sudo kill -9 <PID>
```

**问题4: GPU不可用**
```bash
# 解决方案: 检查GPU状态
nvidia-smi
# 更新GPU参数
--gpus 0  # 只使用GPU 0
```

### 2. 日志检查

```bash
# 查看AirSim服务器日志
tail -f /tmp/airsim_server.log

# 查看环境启动日志
tail -f /tmp/airsim_env.log
```

## 📊 环境验证

### 1. 创建验证脚本

```python
# test_airsim_env.py
import airsim
import time

def test_airsim_connection():
    try:
        client = airsim.MultirotorClient()
        client.confirmConnection()
        print("✅ AirSim连接成功")
        
        # 获取无人机状态
        state = client.getMultirotorState()
        print(f"✅ 无人机位置: {state.kinematics_estimated.position}")
        
        return True
    except Exception as e:
        print(f"❌ AirSim连接失败: {e}")
        return False

if __name__ == "__main__":
    test_airsim_connection()
```

### 2. 运行验证

```bash
cd /home/<USER>/TravelUAV
source setup_env.sh
python test_airsim_env.py
```

## 🎮 使用示例

### 1. 启动特定环境

```bash
# 启动NYC环境
python airsim_plugin/AirVLNSimulatorServerTool.py \
    --port 30000 \
    --root_path /home/<USER>/TravelUAV/envs \
    --gpus 0
```

### 2. 客户端连接测试

```python
import airsim

# 连接到AirSim
client = airsim.MultirotorClient()
client.confirmConnection()

# 启用API控制
client.enableApiControl(True)
client.armDisarm(True)

# 起飞
client.takeoffAsync().join()

# 移动到指定位置
client.moveToPositionAsync(0, 0, -10, 5).join()

# 获取图像
responses = client.simGetImages([
    airsim.ImageRequest("FrontCamera", airsim.ImageType.Scene, False, False)
])

print("✅ 基本导航测试完成")
```

## 📝 下一步

1. **验证环境下载**: 确保所有环境文件正确下载
2. **测试服务器启动**: 验证AirSim服务器可以正常启动
3. **运行导航演示**: 使用LLaMA-UAV模型进行导航测试
4. **数据收集**: 使用DAgger方法收集训练数据

## 🔗 相关资源

- [AirSim官方文档](https://microsoft.github.io/AirSim/)
- [TravelUAV论文](https://arxiv.org/abs/2410.07087)
- [项目主页](https://prince687028.github.io/Travel/)
- [HuggingFace数据集](https://huggingface.co/datasets/wangxiangyu0814/TravelUAV_env)
