#!/bin/bash

# TravelUAV 项目启动脚本
# 提供多种启动选项和功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/home/<USER>/TravelUAV"
cd "$PROJECT_ROOT"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查环境
check_environment() {
    print_message $BLUE "🔧 检查环境状态..."
    
    # 检查conda环境
    if conda info --envs | grep -q "llamauav"; then
        print_message $GREEN "✅ llamauav conda环境存在"
    else
        print_message $RED "❌ llamauav conda环境不存在"
        exit 1
    fi
    
    # 激活环境并检查
    source setup_env.sh
    
    # 检查GPU
    if python -c "import torch; print('CUDA available:', torch.cuda.is_available())" | grep -q "True"; then
        print_message $GREEN "✅ CUDA可用"
    else
        print_message $YELLOW "⚠️  CUDA不可用，将使用CPU"
    fi
    
    print_message $GREEN "✅ 环境检查完成"
}

# 显示菜单
show_menu() {
    print_message $BLUE "🚁 TravelUAV 项目启动菜单"
    echo "=================================="
    echo "1. 🖥️  启动基本Gradio演示界面"
    echo "2. 🎯 启动高级可视化界面"
    echo "3. 🔧 检查系统环境状态"
    echo "4. 📊 启动AirSim仿真服务器"
    echo "5. 🚀 运行模型评估"
    echo "6. 📈 运行DAgger数据收集"
    echo "7. 🎮 启动GroundingDINO演示"
    echo "8. 📖 查看使用指南"
    echo "9. 🛑 停止所有服务"
    echo "0. 退出"
    echo "=================================="
}

# 启动基本演示界面
start_basic_demo() {
    print_message $GREEN "🖥️  启动基本Gradio演示界面..."
    source setup_env.sh
    python demo_gradio.py
}

# 启动高级可视化界面
start_advanced_demo() {
    print_message $GREEN "🎯 启动高级可视化界面..."
    
    # 检查依赖
    source setup_env.sh
    pip install plotly psutil -q
    
    python advanced_demo.py
}

# 检查系统状态
check_system_status() {
    print_message $BLUE "📊 系统状态检查..."
    
    source setup_env.sh
    
    echo "=== Python环境 ==="
    python --version
    
    echo "=== PyTorch状态 ==="
    python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}' if torch.cuda.is_available() else 'CPU模式')"
    
    echo "=== GPU状态 ==="
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits
    else
        echo "nvidia-smi 不可用"
    fi
    
    echo "=== 磁盘空间 ==="
    df -h . | tail -1
    
    echo "=== 项目文件 ==="
    ls -la | head -10
}

# 启动AirSim服务器
start_airsim_server() {
    print_message $GREEN "📊 启动AirSim仿真服务器..."
    
    if [ ! -d "airsim_plugin" ]; then
        print_message $RED "❌ airsim_plugin目录不存在"
        return 1
    fi
    
    cd airsim_plugin
    
    # 检查环境路径
    if [ ! -d "/path/to/your/envs" ]; then
        print_message $YELLOW "⚠️  请先配置环境路径"
        print_message $BLUE "编辑 airsim_plugin/AirVLNSimulatorServerTool.py 中的 env_exec_path_dict"
        return 1
    fi
    
    source ../setup_env.sh
    python AirVLNSimulatorServerTool.py --port 30000 --root_path /path/to/your/envs --gpus 0
}

# 运行模型评估
run_evaluation() {
    print_message $GREEN "🚀 运行模型评估..."
    
    # 检查模型文件
    model_path="Model/LLaMA-UAV/work_dirs/llama-vid-7b-pretrain-224-uav-full-data-lora32"
    if [ ! -d "$model_path" ]; then
        print_message $RED "❌ 模型文件不存在: $model_path"
        print_message $BLUE "请先下载预训练模型"
        return 1
    fi
    
    source setup_env.sh
    bash scripts/eval.sh
}

# 运行DAgger数据收集
run_dagger() {
    print_message $GREEN "📈 运行DAgger数据收集..."
    
    source setup_env.sh
    bash scripts/dagger_NYC.sh
}

# 启动GroundingDINO演示
start_groundingdino_demo() {
    print_message $GREEN "🎮 启动GroundingDINO演示..."
    
    grounding_dir="src/model_wrapper/utils/GroundingDINO"
    if [ ! -d "$grounding_dir" ]; then
        print_message $RED "❌ GroundingDINO目录不存在"
        return 1
    fi
    
    cd "$grounding_dir"
    
    # 检查权重文件
    if [ ! -f "weights/groundingdino_swint_ogc.pth" ]; then
        print_message $BLUE "📥 下载GroundingDINO权重文件..."
        mkdir -p weights
        cd weights
        wget -q https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth
        cd ..
    fi
    
    # 创建示例图像（如果不存在）
    if [ ! -f "demo_image.jpg" ]; then
        print_message $BLUE "创建示例图像..."
        python -c "
from PIL import Image, ImageDraw
img = Image.new('RGB', (640, 480), 'lightblue')
draw = ImageDraw.Draw(img)
draw.rectangle([100, 100, 300, 300], outline='red', width=5)
draw.rectangle([400, 200, 600, 400], outline='green', width=5)
img.save('demo_image.jpg')
print('示例图像已创建')
"
    fi
    
    source ../../setup_env.sh
    
    print_message $BLUE "运行目标检测演示..."
    CUDA_VISIBLE_DEVICES=0 python demo/inference_on_a_image.py \
        -c groundingdino/config/GroundingDINO_SwinT_OGC.py \
        -p weights/groundingdino_swint_ogc.pth \
        -i demo_image.jpg \
        -o output \
        -t "building . car . tree"
    
    print_message $GREEN "✅ 检测完成，结果保存在 output/ 目录"
}

# 查看使用指南
show_usage_guide() {
    print_message $BLUE "📖 TravelUAV 使用指南"
    
    if [ -f "USAGE_GUIDE.md" ]; then
        head -50 USAGE_GUIDE.md
        echo ""
        print_message $YELLOW "完整指南请查看: USAGE_GUIDE.md"
    else
        print_message $RED "❌ 使用指南文件不存在"
    fi
}

# 停止所有服务
stop_all_services() {
    print_message $YELLOW "🛑 停止所有服务..."
    
    # 停止Gradio进程
    pkill -f "gradio" || true
    pkill -f "demo_gradio.py" || true
    pkill -f "advanced_demo.py" || true
    
    # 停止AirSim服务器
    pkill -f "AirVLNSimulatorServerTool.py" || true
    
    print_message $GREEN "✅ 所有服务已停止"
}

# 主函数
main() {
    # 检查是否在正确目录
    if [ ! -f "setup_env.sh" ]; then
        print_message $RED "❌ 请在TravelUAV项目根目录运行此脚本"
        exit 1
    fi
    
    # 如果有参数，直接执行对应功能
    if [ $# -gt 0 ]; then
        case $1 in
            "demo") start_basic_demo ;;
            "advanced") start_advanced_demo ;;
            "check") check_system_status ;;
            "airsim") start_airsim_server ;;
            "eval") run_evaluation ;;
            "dagger") run_dagger ;;
            "grounding") start_groundingdino_demo ;;
            "guide") show_usage_guide ;;
            "stop") stop_all_services ;;
            *) print_message $RED "❌ 未知参数: $1" ;;
        esac
        return
    fi
    
    # 交互式菜单
    while true; do
        echo ""
        show_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1) start_basic_demo ;;
            2) start_advanced_demo ;;
            3) check_system_status ;;
            4) start_airsim_server ;;
            5) run_evaluation ;;
            6) run_dagger ;;
            7) start_groundingdino_demo ;;
            8) show_usage_guide ;;
            9) stop_all_services ;;
            0) 
                print_message $GREEN "👋 再见！"
                exit 0
                ;;
            *)
                print_message $RED "❌ 无效选择，请输入 0-9"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 运行主函数
main "$@"
