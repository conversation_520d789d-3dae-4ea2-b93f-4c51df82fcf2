# TravelUAV 环境文件增量下载工具

## 功能特性

✅ **智能增量下载**：只下载本地缺失或损坏的文件，避免重复下载  
✅ **并行下载**：支持多线程并行下载，提高下载效率  
✅ **详细进度显示**：实时显示每个文件的下载进度、速度和剩余时间  
✅ **自动文件校验**：比较文件大小，识别损坏或不完整的文件  
✅ **缓存文件过滤**：自动过滤掉临时文件和缓存文件  

## 脚本说明

### 1. download_advanced.py（推荐）
**高级增量下载脚本**，支持并行下载和详细进度显示

**特性：**
- 并行下载（默认最大3个并发）
- 每个文件独立的进度条
- 总体下载进度监控
- 下载速度和剩余时间显示
- 失败重试和错误处理

**使用方法：**
```bash
python3 download_advanced.py
```

### 2. download.py
**基础增量下载脚本**，支持基本的进度显示

**特性：**
- 顺序下载
- 基本进度条显示
- 文件大小和下载时间统计

**使用方法：**
```bash
python3 download.py
```

### 3. download_test.py
**测试版本**，包含交互式确认功能

**特性：**
- 下载前需要用户确认
- 适合测试和调试

## 配置参数

### download_advanced.py 配置
```python
repo_id = "wangxiangyu0814/TravelUAV_env"  # Hugging Face 仓库ID
local_dir = "/home/<USER>/TravelUAV/envs/TravelUAV_env"  # 本地目录
max_workers = 3  # 最大并发下载数（建议2-4）
```

### 环境要求
- Python 3.6+
- huggingface_hub
- tqdm
- requests

### 安装依赖
```bash
pip install huggingface_hub tqdm requests
```

## 使用示例

### 1. 首次下载
```bash
cd /home/<USER>/TravelUAV
python3 download_advanced.py
```

### 2. 增量更新
如果之前已经下载了部分文件，脚本会自动：
- 扫描本地已有文件
- 识别缺失或损坏的文件
- 只下载需要的文件

### 3. 自定义并发数
修改 `download_advanced.py` 中的 `max_workers` 参数：
```python
max_workers = 2  # 降低并发数，适合网络较慢的情况
max_workers = 5  # 提高并发数，适合网络较快的情况
```

## 输出说明

### 进度显示
```
总体进度: 45%|████▌     | 5/11 [02:30<03:15, 1.2文件/s]
状态: 活跃=3, 最新=carla_town_envs.z08...

下载: carla_town_envs.z08...: 状态=✓ 完成, 大小=4026.5MB, 速度=3.2MB/s
下载: carla_town_envs.z09...: 7%|███▏     | 262M/4.03G [01:31<20:33, 3.05MB/s]
下载: carla_town_envs.z10...: 2%|▋        | 73.4M/4.03G [00:26<23:09, 2.84MB/s]
```

### 完成信息
```
下载完成!
成功: 11/11
🎉 增量下载完成!
```

## 故障排除

### 1. 网络连接问题
- 脚本使用 HF 镜像站点 (https://hf-mirror.com)
- 如果连接失败，检查网络连接

### 2. 磁盘空间不足
- 确保目标目录有足够空间
- 每个 .z0X 文件约 4GB，.zip 文件大小不等

### 3. 下载中断
- 重新运行脚本即可继续下载
- 脚本会自动跳过已完成的文件

### 4. 文件损坏
- 脚本会自动检测文件大小不匹配
- 损坏的文件会被重新下载

## 文件结构

下载完成后，目录结构如下：
```
/home/<USER>/TravelUAV/envs/TravelUAV_env/
├── .gitattributes
├── BattlefieldKitDesert.z01
├── BattlefieldKitDesert.zip
├── BrushifyCountryRoads.zip
├── BrushifyForestPack.zip
├── BrushifyUrban.zip
├── Japanese_Street.zip
├── London_Street.zip
├── NordicHarbour.zip
├── WesterTown.zip
├── carla_town_envs.z01-z11
├── carla_town_envs.zip
├── closeloop_envs.z01-z04
└── closeloop_envs.zip
```

## 性能优化建议

1. **网络优化**：使用稳定的网络连接
2. **并发调整**：根据网络带宽调整 `max_workers`
3. **磁盘性能**：使用SSD存储以提高写入速度
4. **内存使用**：大文件下载时注意内存使用情况
