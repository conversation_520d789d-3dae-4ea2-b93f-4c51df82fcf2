from huggingface_hub import HfApi, hf_hub_download
import os
from pathlib import Path
from typing import List, Dict, Set
import hashlib
import time
from tqdm import tqdm

class IncrementalDownloader:
    def __init__(self, repo_id: str, local_dir: str, repo_type: str = "dataset"):
        self.repo_id = repo_id
        self.local_dir = Path(local_dir)
        self.repo_type = repo_type
        self.api = HfApi()

        # 设置HuggingFace镜像
        os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

        # 确保目标目录存在
        self.local_dir.mkdir(parents=True, exist_ok=True)

    def get_remote_files(self) -> Dict[str, Dict]:
        """获取远程仓库的文件列表和元数据"""
        print(f"正在获取远程仓库 {self.repo_id} 的文件列表...")

        try:
            repo_info = self.api.repo_info(
                repo_id=self.repo_id,
                repo_type=self.repo_type
            )

            remote_files = {}
            for file_info in repo_info.siblings:
                remote_files[file_info.rfilename] = {
                    'size': getattr(file_info, 'size', 0),
                    'lfs': getattr(file_info, 'lfs', None)
                }

            print(f"远程仓库共有 {len(remote_files)} 个文件")
            return remote_files

        except Exception as e:
            print(f"获取远程文件列表失败: {e}")
            return {}

    def get_local_files(self) -> Dict[str, Dict]:
        """获取本地文件列表和元数据（过滤掉缓存文件）"""
        print(f"正在扫描本地目录 {self.local_dir}...")

        local_files = {}
        if self.local_dir.exists():
            for file_path in self.local_dir.rglob('*'):
                if file_path.is_file():
                    relative_path = file_path.relative_to(self.local_dir)
                    relative_path_str = str(relative_path)

                    # 过滤掉缓存文件和临时文件
                    if (relative_path_str.startswith('.cache/') or
                        relative_path_str.endswith('.lock') or
                        relative_path_str.endswith('.metadata') or
                        '.incomplete' in relative_path_str):
                        continue

                    local_files[relative_path_str] = {
                        'size': file_path.stat().st_size,
                        'path': file_path
                    }

        print(f"本地目录共有 {len(local_files)} 个有效文件")
        return local_files

    def identify_missing_files(self, remote_files: Dict, local_files: Dict) -> List[str]:
        """识别缺失的文件"""
        missing_files = []

        print("\n正在比较本地和远程文件...")

        for remote_file, remote_info in remote_files.items():
            if remote_file not in local_files:
                # 文件完全缺失
                missing_files.append(remote_file)
                print(f"缺失文件: {remote_file}")
            else:
                # 对于LFS文件（remote_size为None），只检查文件是否存在
                remote_size = remote_info['size']
                local_size = local_files[remote_file]['size']

                if remote_size is not None and remote_size > 0 and local_size != remote_size:
                    missing_files.append(remote_file)
                    print(f"文件大小不匹配: {remote_file} (本地: {local_size}, 远程: {remote_size})")
                elif local_size == 0:
                    # 本地文件大小为0，可能是下载失败
                    missing_files.append(remote_file)
                    print(f"本地文件为空: {remote_file}")

        print(f"\n发现 {len(missing_files)} 个需要下载的文件")
        return missing_files

    def download_missing_files(self, missing_files: List[str]) -> bool:
        """下载缺失的文件"""
        if not missing_files:
            print("所有文件都已存在且完整，无需下载。")
            return True

        print(f"需要下载 {len(missing_files)} 个文件:")
        for file in missing_files:
            print(f"  - {file}")

        success_count = 0

        # 使用tqdm显示总体进度
        with tqdm(total=len(missing_files), desc="下载进度", unit="文件") as pbar:
            for i, filename in enumerate(missing_files, 1):
                try:
                    pbar.set_description(f"下载中: {filename}")

                    # 确保目标目录存在
                    target_path = self.local_dir / filename
                    target_path.parent.mkdir(parents=True, exist_ok=True)

                    # 记录开始时间
                    start_time = time.time()

                    # 下载文件（移除已弃用的参数）
                    downloaded_path = hf_hub_download(
                        repo_id=self.repo_id,
                        filename=filename,
                        repo_type=self.repo_type,
                        local_dir=self.local_dir
                    )

                    # 计算下载时间
                    elapsed_time = time.time() - start_time

                    # 获取文件大小
                    file_size = Path(downloaded_path).stat().st_size
                    file_size_mb = file_size / (1024 * 1024)

                    success_count += 1
                    pbar.set_postfix({
                        "成功": f"{success_count}/{len(missing_files)}",
                        "大小": f"{file_size_mb:.1f}MB",
                        "耗时": f"{elapsed_time:.1f}s"
                    })
                    pbar.update(1)

                except Exception as e:
                    pbar.set_postfix({
                        "失败": filename,
                        "错误": str(e)[:30]
                    })
                    pbar.update(1)
                    print(f"\n✗ 下载失败: {filename} - {e}")

        print(f"\n下载完成! 成功: {success_count}/{len(missing_files)}")
        return success_count == len(missing_files)

    def run(self) -> bool:
        """执行增量下载"""
        print("=" * 60)
        print(f"开始增量下载 {self.repo_id}")
        print(f"目标目录: {self.local_dir}")
        print("=" * 60)

        # 获取远程文件列表
        remote_files = self.get_remote_files()
        if not remote_files:
            print("无法获取远程文件列表，退出。")
            return False

        # 获取本地文件列表
        local_files = self.get_local_files()

        # 识别缺失文件
        missing_files = self.identify_missing_files(remote_files, local_files)

        # 下载缺失文件
        return self.download_missing_files(missing_files)


def main():
    # 配置参数
    repo_id = "wangxiangyu0814/TravelUAV_env"
    local_dir = "/home/<USER>/TravelUAV/envs/TravelUAV_env"

    # 创建下载器并执行
    downloader = IncrementalDownloader(repo_id, local_dir)
    success = downloader.run()

    if success:
        print("\n🎉 增量下载完成!")
    else:
        print("\n❌ 下载过程中出现错误")


if __name__ == "__main__":
    main()