#!/usr/bin/env python3
"""
TravelUAV Gradio 演示界面
提供基本的模型推理和可视化功能
"""

import gradio as gr
import torch
import numpy as np
from PIL import Image
import os
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "Model" / "LLaMA-UAV"))

def check_environment():
    """检查环境状态"""
    info = {
        "Python版本": sys.version.split()[0],
        "PyTorch版本": torch.__version__ if torch else "未安装",
        "CUDA可用": torch.cuda.is_available() if torch else False,
        "GPU数量": torch.cuda.device_count() if torch and torch.cuda.is_available() else 0,
        "当前设备": f"cuda:{torch.cuda.current_device()}" if torch and torch.cuda.is_available() else "cpu"
    }
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        info["GPU型号"] = gpu_name
        info["GPU显存"] = f"{gpu_memory:.1f} GB"
    
    return json.dumps(info, indent=2, ensure_ascii=False)

def load_test_model():
    """加载测试用的小模型"""
    try:
        from transformers import AutoTokenizer, AutoModel
        
        # 使用一个小的测试模型
        model_name = "microsoft/DialoGPT-small"
        tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            cache_dir="/home/<USER>/.cache/huggingface"
        )
        
        return f"✅ 成功加载测试模型: {model_name}"
    except Exception as e:
        return f"❌ 模型加载失败: {str(e)}"

def process_navigation_instruction(instruction, image=None):
    """处理导航指令（模拟）"""
    if not instruction.strip():
        return "请输入导航指令"
    
    # 模拟处理过程
    response = f"""
🚁 无人机导航指令处理结果：

📝 输入指令: {instruction}

🎯 解析结果:
- 任务类型: 视觉语言导航
- 指令长度: {len(instruction)} 字符
- 处理状态: ✅ 成功解析

🛣️ 建议路径:
1. 分析当前环境
2. 识别目标物体
3. 规划最优路径
4. 执行导航动作

⚠️ 注意: 这是演示模式，实际导航需要完整的模型和仿真环境
"""
    
    if image is not None:
        response += f"\n📷 图像信息: {image.size if hasattr(image, 'size') else '已接收图像'}"
    
    return response

def test_groundingdino():
    """测试GroundingDINO功能"""
    try:
        # 检查GroundingDINO路径
        grounding_path = project_root / "src" / "model_wrapper" / "utils" / "GroundingDINO"
        if grounding_path.exists():
            return f"✅ GroundingDINO路径存在: {grounding_path}"
        else:
            return f"❌ GroundingDINO路径不存在: {grounding_path}"
    except Exception as e:
        return f"❌ GroundingDINO测试失败: {str(e)}"

def show_project_info():
    """显示项目信息"""
    info = f"""
# 🚁 TravelUAV 项目信息

## 📋 项目概述
- **名称**: TravelUAV - 现实无人机视觉语言导航
- **论文**: [Towards Realistic UAV Vision-Language Navigation](https://arxiv.org/abs/2410.07087)
- **项目页面**: https://prince687028.github.io/Travel/

## 🏗️ 项目结构
- **Model/LLaMA-UAV/**: LLaMA-UAV模型代码
- **src/**: 核心源代码
- **scripts/**: 训练和评估脚本
- **airsim_plugin/**: AirSim仿真插件

## 🎯 主要功能
1. **多模态大语言模型**: 基于LLaMA的视觉语言理解
2. **无人机导航**: 基于自然语言指令的UAV导航
3. **仿真环境**: 基于AirSim的真实环境仿真
4. **目标检测**: 集成GroundingDINO进行目标识别

## 📊 模型要求
- **预训练模型**: Vicuna-7B, EVA-ViT-G
- **显存需求**: 建议25-32GB
- **数据集**: TravelUAV数据集

## 🚀 快速开始
1. 下载预训练模型
2. 配置AirSim环境
3. 运行训练或评估脚本
"""
    return info

# 创建Gradio界面
def create_demo():
    with gr.Blocks(title="TravelUAV Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🚁 TravelUAV 演示界面")
        gr.Markdown("基于LLaMA的无人机视觉语言导航系统")
        
        with gr.Tabs():
            # 环境检查标签页
            with gr.TabItem("🔧 环境检查"):
                gr.Markdown("## 系统环境状态")
                env_button = gr.Button("检查环境", variant="primary")
                env_output = gr.Textbox(label="环境信息", lines=10)
                env_button.click(check_environment, outputs=env_output)
                
                gr.Markdown("## 模型加载测试")
                model_button = gr.Button("测试模型加载", variant="secondary")
                model_output = gr.Textbox(label="模型状态", lines=3)
                model_button.click(load_test_model, outputs=model_output)
                
                gr.Markdown("## GroundingDINO测试")
                dino_button = gr.Button("测试GroundingDINO", variant="secondary")
                dino_output = gr.Textbox(label="GroundingDINO状态", lines=3)
                dino_button.click(test_groundingdino, outputs=dino_output)
            
            # 导航演示标签页
            with gr.TabItem("🧭 导航演示"):
                gr.Markdown("## 无人机导航指令处理（演示模式）")
                
                with gr.Row():
                    with gr.Column():
                        instruction_input = gr.Textbox(
                            label="导航指令",
                            placeholder="例如：飞到红色建筑物附近",
                            lines=3
                        )
                        image_input = gr.Image(
                            label="当前视角图像（可选）",
                            type="pil"
                        )
                        process_button = gr.Button("处理指令", variant="primary")
                    
                    with gr.Column():
                        result_output = gr.Textbox(
                            label="处理结果",
                            lines=15
                        )
                
                process_button.click(
                    process_navigation_instruction,
                    inputs=[instruction_input, image_input],
                    outputs=result_output
                )
                
                # 示例指令
                gr.Markdown("### 📝 示例指令")
                examples = [
                    "飞到红色建筑物前面",
                    "寻找停车场并降落",
                    "跟随道路飞行到十字路口",
                    "避开障碍物飞到目标点"
                ]
                
                for example in examples:
                    gr.Button(example, size="sm").click(
                        lambda x=example: x,
                        outputs=instruction_input
                    )
            
            # 项目信息标签页
            with gr.TabItem("📖 项目信息"):
                project_info = gr.Markdown(show_project_info())
        
        gr.Markdown("---")
        gr.Markdown("💡 **提示**: 这是TravelUAV的演示界面。完整功能需要下载预训练模型和配置AirSim环境。")
    
    return demo

if __name__ == "__main__":
    # 启动演示界面
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",  # 允许外部访问
        server_port=7860,       # 默认端口
        share=False,            # 不创建公共链接
        debug=True              # 启用调试模式
    )
