#!/bin/bash

# TravelUAV 环境配置脚本

echo "正在配置 TravelUAV 运行环境..."

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate llamauav

# 加载环境变量
source .env

# 设置HuggingFace镜像
export HF_ENDPOINT=https://hf-mirror.com
export HUGGINGFACE_HUB_CACHE=/home/<USER>/.cache/huggingface

# 创建必要的目录
mkdir -p /home/<USER>/.cache/huggingface
mkdir -p checkpoints
mkdir -p logs

# 验证环境
echo "Python版本: $(python --version)"
echo "PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"
echo "CUDA可用: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "GPU数量: $(python -c 'import torch; print(torch.cuda.device_count())')"

echo "环境配置完成！"
echo "使用方法: source setup_env.sh"
